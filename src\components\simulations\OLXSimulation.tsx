import React from 'react';
import { <PERSON><PERSON><PERSON> } from '../../types';
import { MessageSquare, Phone, Star, MapPin } from 'lucide-react';

interface OLXSimulationProps {
  scenario: Scenario;
  onResponse: (optionIndex: number) => void;
}

const OLXSimulation: React.FC<OLXSimulationProps> = ({ scenario, onResponse }) => {
  const content = scenario.content as any;

  const renderConversation = () => {
    if (content.conversation) {
      return content.conversation.map((msg: any, index: number) => (
        <div key={index} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'} mb-3`}>
          <div className={`max-w-[75%] p-3 rounded-lg ${
            msg.sender === 'user' 
              ? 'bg-blue-500 text-white rounded-br-sm' 
              : 'bg-slate-700 text-white rounded-bl-sm'
          }`}>
            <p className="text-sm">{msg.message}</p>
            <p className={`text-xs mt-1 ${msg.sender === 'user' ? 'text-blue-100' : 'text-slate-400'}`}>
              {index === content.conversation.length - 1 ? 'now' : '2m'}
            </p>
          </div>
        </div>
      ));
    } else {
      return (
        <div className="flex justify-start mb-3">
          <div className="max-w-[75%] p-3 rounded-lg bg-slate-700 text-white rounded-bl-sm">
            <p className="text-sm">{content.message}</p>
            <p className="text-xs mt-1 text-slate-400">now</p>
          </div>
        </div>
      );
    }
  };

  return (
    <div className="max-w-md mx-auto">
      {/* Phone Frame */}
      <div className="bg-slate-900 rounded-3xl p-2 shadow-2xl">
        {/* OLX Chat Interface */}
        <div className="bg-white rounded-2xl overflow-hidden">
          {/* Header */}
          <div className="bg-blue-600 px-4 py-3 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-200 rounded-full flex items-center justify-center">
                <span className="text-blue-800 font-semibold">
                  {(content.buyer || content.seller || 'B').charAt(0)}
                </span>
              </div>
              <div>
                <h3 className="text-white font-medium">{content.buyer || content.seller}</h3>
                <div className="flex items-center gap-1">
                  <Star className="w-3 h-3 text-yellow-300 fill-current" />
                  <span className="text-blue-100 text-xs">4.2</span>
                </div>
              </div>
            </div>
            <div className="flex gap-3">
              <Phone className="w-5 h-5 text-white" />
              <MapPin className="w-5 h-5 text-white" />
            </div>
          </div>

          {/* Product Info Bar */}
          <div className="bg-slate-100 px-4 py-2 border-b">
            <p className="text-slate-600 text-xs">LG Washing Machine - ₹8,500</p>
          </div>

          {/* Chat Area */}
          <div className="bg-slate-50 p-4 min-h-[250px]">
            {renderConversation()}
          </div>

          {/* Input Area */}
          <div className="bg-white px-4 py-3 border-t">
            <div className="flex items-center gap-3">
              <div className="flex-1 bg-slate-100 rounded-full px-4 py-2">
                <span className="text-slate-500 text-sm">Type a message...</span>
              </div>
              <MessageSquare className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Response Options */}
      <div className="mt-8 space-y-3">
        <h4 className="text-white font-medium mb-4">How would you respond?</h4>
        {scenario.options.map((option, index) => (
          <button
            key={index}
            onClick={() => onResponse(index + 1)}
            className="w-full p-4 text-left bg-slate-800/50 border border-slate-700 rounded-lg text-slate-200 hover:bg-slate-700/50 hover:border-slate-600 transition-all duration-200"
          >
            <span className="text-blue-400 font-medium">{index + 1}. </span>
            {option}
          </button>
        ))}
      </div>
    </div>
  );
};

export default OLXSimulation;