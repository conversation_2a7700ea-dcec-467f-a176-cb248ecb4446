-- TEEN-<PERSON><PERSON><PERSON> SCAM ASSESSMENT SCENARIOS (Ages 13-17)
-- Updated SQL for scenarios table - TEEN FOCUSED CONTENT

-- Create scenarios table with the structure your app expects
CREATE TABLE IF NOT EXISTS scenarios (
  id BIGSERIAL PRIMARY KEY,
  scenario_name TEXT NOT NULL, -- e.g., 'scenario-1', 'scenario-2', etc.
  language TEXT NOT NULL DEFAULT 'english', -- 'english', 'hindi', 'urdu', 'telugu'
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  content JSONB NOT NULL, -- Complete scenario data including type, options, riskLevels
  audio_url TEXT, -- URL to audio file in Supabase Storage
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create unique constraint for scenario_name + language combination
CREATE UNIQUE INDEX IF NOT EXISTS scenarios_name_language_idx ON scenarios (scenario_name, language);

-- Add RLS policies
ALTER TABLE scenarios ENABLE ROW LEVEL SECURITY;

-- Allow read access to active scenarios for all users
CREATE POLICY "Allow read access to active scenarios" ON scenarios
FOR SELECT USING (is_active = true);

-- TEEN-FOCUSED SCENARIOS (Ages 13-17) - English
INSERT INTO scenarios (scenario_name, language, title, description, content, audio_url, is_active) VALUES 

-- Scenario 1: SMS Scam (Very relevant for teens)
('scenario-1', 'english', 'Free Gift SMS Scam', 'You receive a text about winning a prize', '{
  "type": "sms",
  "content": "🎉 CONGRATULATIONS! You have WON a FREE iPhone 15! 📱\\n\\nYou were selected from your school! Click this link NOW to claim:\\n👉 teenprize.net/claim-iphone\\n\\n⚠️ HURRY! Only 24 hours left!\\n\\nReply STOP to unsubscribe",
  "options": [
    "Click the link immediately - I want my iPhone!",
    "Forward the message to my friends first",
    "Ask my parents before clicking anything",
    "Delete the message - this seems fake"
  ],
  "riskLevels": {
    "high_risk": [0, 1],
    "medium_risk": [2],
    "low_risk": [3]
  }
}', 'https://your-project.supabase.co/storage/v1/object/public/scenario-audio/scenario-1-english.mp3', true),

-- Scenario 2: Instagram Influencer Scam
('scenario-2', 'english', 'Fake Influencer DM', 'A popular influencer wants to collaborate with you', '{
  "type": "instagram",
  "content": "Hey! 👋 I''m @famous_tiktoker with 2M followers! 🌟\\n\\nI LOVE your posts! Want to collab? I can make you famous too! 🚀\\n\\nJust send me ₹500 for ''promotion fees'' and I''ll feature you on my page. You''ll get 10K followers overnight! 💯\\n\\nQuick! This offer expires today. Send to UPI: scammer@paytm",
  "options": [
    "Send the money right away - this is my big break!",
    "Ask for proof of their follower count first",
    "Check if their account is verified before paying",
    "Block and report - this seems like a scam"
  ],
  "riskLevels": {
    "high_risk": [0],
    "medium_risk": [1, 2],
    "low_risk": [3]
  }
}', 'https://your-project.supabase.co/storage/v1/object/public/scenario-audio/scenario-2-english.mp3', true),

-- Scenario 3: Gaming/Discord Scam
('scenario-3', 'english', 'Free Game Skins Offer', 'Someone offers free gaming items', '{
  "type": "discord",
  "content": "🎮 FREE FORTNITE SKINS GIVEAWAY! 🎮\\n\\nHey gamer! I work at Epic Games and I''m giving away rare skins! 🔥\\n\\nTo get your FREE skins:\\n1️⃣ Give me your Epic Games login\\n2️⃣ Download this ''special tool'': bit.ly/free-skins-tool\\n3️⃣ Share this with 10 friends\\n\\nLegit 100%! I''ve given 1000+ kids free skins! 💯",
  "options": [
    "Give my login details - I really want those skins!",
    "Download the tool but not share my login",
    "Ask my gaming friends if this is real first",
    "Report this person - no one gives away free stuff"
  ],
  "riskLevels": {
    "high_risk": [0, 1],
    "medium_risk": [2],
    "low_risk": [3]
  }
}', 'https://your-project.supabase.co/storage/v1/object/public/scenario-audio/scenario-3-english.mp3', true),

-- Scenario 4: WhatsApp Friend Scam
('scenario-4', 'english', 'New Friend Request', 'Someone your age wants to be friends', '{
  "type": "whatsapp",
  "content": "Hi! 😊 I''m Priya, 16 years old. I got your number from a mutual friend.\\n\\nI saw your Instagram and you seem really cool! I''m new to your city and looking for friends. 👯‍♀️\\n\\nBtw, want to earn some quick money? My cousin has this easy online job. You just need to give your Aadhar number and bank details for ''verification''. You can earn ₹5000/week! 💰\\n\\nInterested? I can connect you! 😍",
  "options": [
    "Share my details - I need money for new clothes!",
    "Ask for more details about the job first",
    "Tell my parents about this offer",
    "Block this number - sounds suspicious"
  ],
  "riskLevels": {
    "high_risk": [0],
    "medium_risk": [1],
    "low_risk": [2, 3]
  }
}', 'https://your-project.supabase.co/storage/v1/object/public/scenario-audio/scenario-4-english.mp3', true),

-- Scenario 5: Study/Exam Help Scam
('scenario-5', 'english', 'Exam Answers Leak', 'Someone offers exam papers in advance', '{
  "type": "telegram",
  "content": "📚 URGENT: BOARD EXAM PAPERS LEAKED! 📚\\n\\nHey student! Worried about your board exams? 😰\\n\\nI have the ACTUAL question papers for Math, Science & English! Real papers, not fake! ✅\\n\\nMany students bought from me and got 90%+! 🏆\\n\\nPrice: Only ₹2000 for all subjects\\nPayment: Google Pay/PhonePe\\n\\n⏰ Hurry! Exams start next week!",
  "options": [
    "Buy immediately - I''m failing anyway!",
    "Ask to see a sample question first",
    "Tell my teacher about this offer",
    "Report this - buying exam papers is wrong"
  ],
  "riskLevels": {
    "high_risk": [0],
    "medium_risk": [1],
    "low_risk": [2, 3]
  }
}', 'https://your-project.supabase.co/storage/v1/object/public/scenario-audio/scenario-5-english.mp3', true),

-- Scenario 6: Scholarship/College Scam
('scenario-6', 'english', 'Scholarship Opportunity', 'Someone offers guaranteed college admission', '{
  "type": "email",
  "content": "Subject: 🎓 GUARANTEED College Admission + Scholarship! 🎓\\n\\nDear Student,\\n\\nCongratulations! You have been PRE-SELECTED for our exclusive scholarship program! 🌟\\n\\n✅ Guaranteed admission to top colleges\\n✅ 100% scholarship coverage\\n✅ No entrance exam needed!\\n\\nThis is REAL! 500+ students already got admission through us!\\n\\nJust pay ₹15,000 processing fee and send your documents:\\n- 10th & 12th marksheets\\n- Aadhar card\\n- Parent''s bank statements\\n\\nOffer valid for 48 hours only!\\n\\nRegards,\\nEducation Consultant Team",
  "options": [
    "Pay the fee and send all documents - this is my dream!",
    "Pay the fee but hold back on sending documents",
    "Research this company online before paying",
    "Ignore this email - real scholarships don''t work this way"
  ],
  "riskLevels": {
    "high_risk": [0],
    "medium_risk": [1, 2],
    "low_risk": [3]
  }
}', 'https://your-project.supabase.co/storage/v1/object/public/scenario-audio/scenario-6-english.mp3', true);

-- Instructions for setup:
-- 1. Run this SQL in your Supabase SQL Editor
-- 2. Create Storage bucket called 'scenario-audio' and make it public
-- 3. Upload your audio files with names like: scenario-1-english.mp3, scenario-2-english.mp3, etc.
-- 4. Update the audio_url values above with your actual Supabase storage URLs

-- Note: These scenarios are specifically designed for teens aged 13-17
-- They focus on situations teens commonly encounter online
