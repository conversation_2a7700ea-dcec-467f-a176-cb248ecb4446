import React from "react";
import { <PERSON><PERSON><PERSON> } from "../../types";
import { Smartphone, Signal, Wifi, Battery } from "lucide-react";
import { getTranslation } from "../../utils/language";

interface SMSSimulationProps {
  scenario: Scenario;
  onResponse: (optionIndex: number) => void;
  userLanguage?: "english" | "hindi" | "urdu" | "telugu";
}

const SMSSimulation: React.FC<SMSSimulationProps> = ({
  scenario,
  onResponse,
  userLanguage = "english",
}) => {
  const content = scenario.content as { sender: string; message: string };
  const t = (key: string) => getTranslation(userLanguage, key);

  return (
    <div className="max-w-md mx-auto">
      {/* Phone Frame */}
      <div className="bg-gray-900 rounded-3xl p-3 shadow-2xl transform hover:scale-105 transition-transform duration-300">
        {/* Status Bar */}
        <div className="flex justify-between items-center px-6 py-3 text-white text-sm">
          <span className="font-medium">9:41</span>
          <div className="flex items-center gap-1">
            <Signal className="w-4 h-4" />
            <Wifi className="w-4 h-4" />
            <Battery className="w-4 h-4" />
          </div>
        </div>

        {/* SMS Interface */}
        <div className="bg-black rounded-2xl overflow-hidden shadow-inner">
          {/* Header */}
          <div className="bg-gradient-to-r from-gray-800 to-gray-700 px-4 py-4 border-b border-gray-600">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-emerald-500/20 rounded-full">
                <Smartphone className="w-5 h-5 text-emerald-400" />
              </div>
              <div>
                <h3 className="text-white font-semibold">{content.sender}</h3>
                <p className="text-gray-400 text-sm">Text Message</p>
              </div>
            </div>
          </div>

          {/* Message */}
          <div className="p-6 min-h-[200px] bg-gradient-to-b from-gray-900 to-black">
            <div className="bg-gradient-to-br from-gray-700 to-gray-800 rounded-2xl rounded-tl-sm p-4 max-w-[85%] shadow-lg border border-gray-600/50">
              <p className="text-white text-sm leading-relaxed whitespace-pre-line">
                {content.message}
              </p>
              <div className="flex justify-between items-center mt-3">
                <p className="text-gray-400 text-xs">now</p>
                <div className="flex space-x-1">
                  <div className="w-1 h-1 bg-emerald-400 rounded-full animate-pulse"></div>
                  <div className="w-1 h-1 bg-emerald-400 rounded-full animate-pulse delay-100"></div>
                  <div className="w-1 h-1 bg-emerald-400 rounded-full animate-pulse delay-200"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Response Options */}
      <div className="mt-8 space-y-4">
        <h4 className="text-white font-semibold mb-6 text-center bg-gradient-to-r from-emerald-400 to-cyan-400 bg-clip-text text-transparent">
          {t("howWouldYouRespond")}
        </h4>
        {scenario.options.map((option, index) => (
          <button
            key={index}
            onClick={() => onResponse(index + 1)}
            className="w-full p-4 text-left bg-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-xl text-gray-200 hover:bg-gray-700/40 hover:border-gray-600/70 hover:shadow-lg hover:shadow-emerald-500/10 transition-all duration-300 transform hover:scale-[1.02] group"
          >
            <div className="flex items-start gap-3">
              <span className="flex-shrink-0 w-6 h-6 bg-gradient-to-br from-emerald-500 to-cyan-600 text-white text-sm font-bold rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                {index + 1}
              </span>
              <span className="leading-relaxed">{option}</span>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default SMSSimulation;
