export interface User {
  name: string;
  age: number;
  language: 'english' | 'hindi' | 'urdu' | 'telugu';
}

export interface Response {
  scenarioId: string;
  selectedOption: number;
  riskLevel: 'high_risk' | 'medium_risk' | 'low_risk';
}

export interface Scenario {
  id: string;
  type: 'sms' | 'whatsapp' | 'instagram' | 'instagram_post' | 'email' | 'call' | 'olx' | 'linkedin' | 'snapchat';
  title: string;
  content: string | any;
  options: string[];
  riskLevels: {
    high_risk: number[];
    medium_risk: number[];
    low_risk: number[];
  };
}

export interface AgeGroup {
  min: number;
  max: number;
  scenarios: Scenario[];
}

export interface SurveyState {
  user: User | null;
  currentScenario: number;
  responses: Response[];
  isComplete: boolean;
}