import { Response } from '../types';

export const calculateRisk = (responses: Response[]) => {
  const riskCounts = {
    high_risk: 0,
    medium_risk: 0,
    low_risk: 0
  };

  responses.forEach(response => {
    riskCounts[response.riskLevel]++;
  });

  return riskCounts;
};

export const getRiskProfile = (riskCounts: { high_risk: number; medium_risk: number; low_risk: number }) => {
  // Strict risk assessment: user is only low risk if ALL responses are low risk
  if (riskCounts.high_risk > 0) {
    return {
      level: 'High Risk',
      color: 'text-red-400',
      bgColor: 'bg-red-500/20',
      description: 'You may be vulnerable to online scams. Consider improving your digital safety awareness.'
    };
  } else if (riskCounts.medium_risk > 0) {
    return {
      level: 'Medium Risk',
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/20',
      description: 'You have moderate awareness but could benefit from additional caution online.'
    };
  } else {
    return {
      level: 'Low Risk',
      color: 'text-green-400',
      bgColor: 'bg-green-500/20',
      description: 'Great job! You demonstrate good awareness of online scam tactics.'
    };
  }
};