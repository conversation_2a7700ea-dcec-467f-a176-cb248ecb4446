import React from 'react';
import { <PERSON><PERSON><PERSON> } from '../../types';
import { Camera, MessageCircle, Send } from 'lucide-react';

interface SnapchatSimulationProps {
  scenario: Scenario;
  onResponse: (optionIndex: number) => void;
}

const SnapchatSimulation: React.FC<SnapchatSimulationProps> = ({ scenario, onResponse }) => {
  const content = scenario.content as { messages: Array<{ sender: string; message: string }> };

  return (
    <div className="max-w-md mx-auto">
      {/* Phone Frame */}
      <div className="bg-slate-900 rounded-3xl p-2 shadow-2xl">
        {/* Snapchat Interface */}
        <div className="bg-yellow-400 rounded-2xl overflow-hidden">
          {/* Header */}
          <div className="bg-yellow-500 px-4 py-3 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                <span className="text-yellow-500 font-bold text-sm">👻</span>
              </div>
              <h3 className="text-white font-semibold">{content.messages[0]?.sender || 'Chat'}</h3>
            </div>
            <div className="flex gap-3">
              <Camera className="w-5 h-5 text-white" />
              <MessageCircle className="w-5 h-5 text-white" />
            </div>
          </div>

          {/* Chat Area */}
          <div className="bg-white p-4 min-h-[280px]">
            {content.messages.map((msg, index) => (
              <div key={index} className="mb-4">
                <div className="flex items-start gap-2">
                  <div className="w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs font-bold">👻</span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-yellow-600 font-semibold text-sm">{msg.sender}</span>
                      <span className="text-slate-400 text-xs">{index + 1}m</span>
                    </div>
                    <div className="bg-slate-100 rounded-lg rounded-tl-sm p-3">
                      <p className="text-slate-800 text-sm leading-relaxed">
                        {msg.message}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Input Area */}
          <div className="bg-yellow-50 px-4 py-3 border-t">
            <div className="flex items-center gap-3">
              <div className="flex-1 bg-white rounded-full px-4 py-2 border">
                <span className="text-slate-500 text-sm">Send a chat</span>
              </div>
              <Send className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Response Options */}
      <div className="mt-8 space-y-3">
        <h4 className="text-white font-medium mb-4">How would you respond?</h4>
        {scenario.options.map((option, index) => (
          <button
            key={index}
            onClick={() => onResponse(index + 1)}
            className="w-full p-4 text-left bg-slate-800/50 border border-slate-700 rounded-lg text-slate-200 hover:bg-slate-700/50 hover:border-slate-600 transition-all duration-200"
          >
            <span className="text-yellow-400 font-medium">{index + 1}. </span>
            {option}
          </button>
        ))}
      </div>
    </div>
  );
};

export default SnapchatSimulation;