import { AgeGroup } from '../types';

export const ageGroups: AgeGroup[] = [
  {
    min: 13,
    max: 17,
    scenarios: [
      {
        id: 'teen-sms-1',
        type: 'sms',
        title: 'InstaPay Notice',
        content: {
          sender: 'InstaPay Notice',
          message: 'Hi! Your ₹500 collab bonus from "reel challenge" is ready.\n\nTap to accept via UPI: [reels-payout.in]\n\n– Instagram Rewards ✨'
        },
        options: [
          'Just clicked the link and entered my UPI PIN. When will the ₹500 come?',
          'Wait, I didn\'t even enter any challenge. Is this for real or some kind of mistake?',
          'I\'ll check on the official Instagram app.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'teen-snapchat-1',
        type: 'snapchat',
        title: 'Snapchat Chat',
        content: {
          messages: [
            { sender: 'tanishq', message: 'Hey, random I know lol but you came up on Quick Add and I think we\'ve got mutuals? 😅\n\nYou looked kinda familiar — do you go to [common school name] or live near [local landmark]?' }
          ]
        },
        options: [
          'Yes i do',
          'ignore the message',
          'Wait are you a real person lol. Prove it first 😛 send a selfie or sth.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2]
        }
      },
      {
        id: 'teen-instagram-post-1',
        type: 'instagram_post',
        title: 'Instagram Post',
        content: {
          account: '@trendie_clips_xo',
          caption: 'Hey! We\'re clearing old stock of trendy accessories (rings, clips, charms etc.) — all under ₹99, free shipping this week only! 🎀\n\nJust DM your order & pay via UPI. First 20 buyers get a free gift too 💕\n\nCheck our tagged posts for reviews 💬',
          likes: '1,247',
          time: '2h'
        },
        options: [
          'Omg this is so cheap!! I want 3 rings and a hair clip. UPI ID?',
          'Can I send you a screenshot of payment now and get it delivered next week?',
          'Do you have a website or Insta checkout link? Also, where are you shipping from?'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'teen-whatsapp-1',
        type: 'whatsapp',
        title: 'WhatsApp Message',
        content: {
          sender: 'EdInsights Survey',
          message: '🎉 Hey! You\'ve been selected for an exclusive student reward survey by EdInsights India.\n\nComplete a 2-min quiz & get FREE Amazon vouchers worth ₹500 instantly! 🛍\n\nLimited entries only. Start here: [ed-insights-survey.in]\n\n- Trusted by 5000+ students ✅'
        },
        options: [
          'Looks cool! I\'ll quickly fill it rn. Hope it\'s legit lol.',
          'Is this from an official site? Never heard of EdInsights India.',
          'Wait do I need to give my phone number? I already started the quiz.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2]
        }
      },
      {
        id: 'teen-instagram-2',
        type: 'instagram',
        title: 'Job Offer DM',
        content: {
          sender: '@workfromhome_students24',
          message: '📢 HIRING: Part-time job for students (Work From Home)\n✅ Just 1–2 hrs a day\n💰 Earn ₹5,000–₹12,000 weekly\n💼 No skills needed, just a phone & internet\n\nIf interested, DM \'JOB\' and we\'ll send full details. Limited slots left!\n– @workfromhome_students24'
        },
        options: [
          'JOB! What\'s the work? I really need a side hustle right now.',
          'Do I have to pay anything to start? I can arrange later.',
          'Can you share your official website or company registration?'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'teen-whatsapp-2',
        type: 'whatsapp',
        title: 'Group Chat Message',
        content: {
          sender: 'Group Member',
          message: 'OMG I just got 1700 free Robux from this site!!\n\nNo survey, no password needed — just enter your username and choose how much Robux you want 💯\n\nWorks 100%, I already bought limiteds 🔥\n\nTry it before it gets patched: [robux-fast.com]\n\nTell me if it works for you!!'
        },
        options: [
          'That link looks shady. Roblox never gives free Robux like that.',
          'BRO if this actually works I\'m gonna go buy core blocks rn 😂',
          'Wait is this that new glitch thing?? I\'m trying it now.'
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1]
        }
      }
    ]
  },
  {
    min: 18,
    max: 30,
    scenarios: [
      {
        id: 'young-email-1',
        type: 'email',
        title: 'Microsoft Security Alert',
        content: {
          from: '<EMAIL>',
          subject: '⚠️ Account Security Alert – Action Required Immediately',
          message: 'Dear User,\n\nWe\'ve detected multiple unauthorized login attempts on your Microsoft account from Mumbai, India and Toronto, Canada.\n\nTo protect your account, we\'ve temporarily locked access.\n\n✅ Please verify your identity immediately by visiting the secure portal below:\n\n[Verify Now] → https://msverify-center.info/accountcheck\n\nIf you do not act within 24 hours, your account will be permanently disabled for your safety.\n\nThank you,\nMicrosoft Security Team 🔒'
        },
        options: [
          'Oh no, Let me verify it now. (Clicks the link and enters credentials.)',
          'Hey, what login attempts? I haven\'t logged in from those places. (Replies to the email.)',
          'I\'ll check my Microsoft account first'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'young-linkedin-1',
        type: 'linkedin',
        title: 'LinkedIn Job Opportunity',
        content: {
          sender: 'Priya Mehra, HR Assistant at Wipro Global IT Solutions',
          message: 'Hi [Your Name],\n\nI\'m Priya Mehra, HR Assistant at Wipro Global IT Solutions. We came across your profile and think you\'d be a perfect fit for our remote internship program.\n\n✔️ Flexible hours\n✔️ ₹18,000/month stipend\n✔️ Work from home\n\nJust fill out this short application: [wipro-careers-apply.in]\n\nLimited spots, so apply ASAP. We\'ll share onboarding steps after registration. Looking forward to having you on board!'
        },
        options: [
          'Thanks, but can you confirm if this is listed on Wipro\'s official site or careers page? I\'ll apply through that.',
          'Sounds amazing! Just filled the form and submitted my ID docs. Hope I get selected!',
          'Do I need to pay any processing fee? Or should I wait for onboarding instructions first?'
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1]
        }
      },
      {
        id: 'young-olx-1',
        type: 'olx',
        title: 'OLX Buyer Chat',
        content: {
          buyer: 'Army Personnel',
          message: 'Hello, I saw your listing for the iPhone 12. I\'m interested and can pay immediately.\n\nI work with the Indian Army, so I can\'t meet in person. I\'ll send a pickup agent to your address.\n\nI will pay via UPI in advance — just share your UPI ID. You\'ll receive a payment link shortly, just approve it to receive the money.'
        },
        options: [
          'Sure, here\'s my UPI ID. I\'ll confirm once I receive the money.',
          'Okay, I got a payment request link. I\'ll approve it now.',
          'Sorry, I don\'t accept links or advance UPI from unknown buyers. Cash or COD only.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'young-snapchat-1',
        type: 'snapchat',
        title: 'Snapchat Stranger',
        content: {
          messages: [
            { sender: 'stranger', message: 'Hey! Not sure if we\'ve talked before but your Bitmoji looked super familiar lol 😅\n\nYou seem kinda familiar... were you at [popular college fest or cafe]? Or maybe it\'s from Insta or Quick Add? haha no clue tbh 😄' }
          ]
        },
        options: [
          'Kinda random tbh… if we\'ve never met, I\'d rather not continue.',
          'ignore the message',
          'Haha you do look familiar too. What\'s your Insta?'
        ],
        riskLevels: {
          high_risk: [3],
          medium_risk: [1],
          low_risk: [2]
        }
      },
      {
        id: 'young-call-1',
        type: 'call',
        title: 'Emergency Call',
        content: {
          caller: 'Hospital Emergency',
          message: 'Hello?! Is this [your name]?\n\nI\'m calling from [hospital name] — your brother was just in an accident. He\'s conscious but we need to start emergency treatment now.\n\nWe don\'t have his full ID or insurance. Can you quickly send ₹10,000 via UPI to this number? Time is critical.'
        },
        options: [
          'Wait what happened?! Okay I\'ll send it now — please take care of him!',
          'I don\'t have 10k now, can I send half and the rest later?',
          'This sounds serious, but I\'ll call my brother and the hospital directly to confirm first.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'young-email-2',
        type: 'email',
        title: 'Accenture Internship',
        content: {
          from: '<EMAIL>',
          subject: 'Accenture Remote Internship Opportunity',
          message: 'Hi [Your Name],\n\nYou\'ve been shortlisted for a remote internship under Accenture\'s partner program.\n\n🧠 Role: Digital Intern\n🕒 Duration: 1 month\n💸 Stipend: ₹15,000\n\nTo confirm your spot, please complete the onboarding form within 24 hours:\n\n👉 www.accenture-internform.live\n\nRegards,\nSneha Verma\nHR Team – Accenture Partner Network'
        },
        options: [
          'Thank you so much! I\'ve submitted the form and shared my ID and resume. Looking forward to the offer letter!',
          'Thanks, but can you confirm if this internship is listed on Accenture\'s official site? I prefer applying through verified channels.',
          'Hey, just checking — is the stipend confirmed? And when will I get the welcome kit?'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2]
        }
      }
    ]
  },
  {
    min: 31,
    max: 45,
    scenarios: [
      {
        id: 'adult-olx-1',
        type: 'olx',
        title: 'OLX Washing Machine Sale',
        content: {
          buyer: 'Relocating Seller',
          conversation: [
            { sender: 'user', message: 'Hi, is the LG washing machine still available?' },
            { sender: 'seller', message: 'Yes sir, perfect condition. Only ₹8,500. We\'re relocating, so need to sell quickly.' },
            { sender: 'user', message: 'Can I come see it?' },
            { sender: 'seller', message: 'It\'s already packed for transport. I can deliver it to you directly. Just send ₹300 as a token so I can hold it for you.' }
          ]
        },
        options: [
          'Makes sense. I\'ll send ₹300 now to confirm. Thanks.',
          'Okay, I\'ll send the token. Just make sure the delivery happens today please.',
          'Understood, but I don\'t pay anything in advance. Let me know if I can see it before payment.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'adult-whatsapp-1',
        type: 'whatsapp',
        title: 'Investment Opportunity',
        content: {
          sender: 'CredGrow Capital',
          message: 'Good afternoon, sir. I\'m reaching out on behalf of CredGrow Capital, a SEBI-affiliated firm offering short-term fixed return plans.\n\n✅ ROI: 25–30% in just 15 days\n✅ Capital protected\n✅ Tax-free withdrawal\n\nSeveral professionals from IT and finance have already joined. We\'re onboarding 10 more clients this week.\n\nI can share the brochure and today\'s live payout screenshots. Let me know if you\'re open to a quick call.'
        },
        options: [
          'Sounds solid. Can you send me the plan details and payment steps?',
          'I\'ll need to verify your firm\'s SEBI registration and website first — can you share that?',
          'Looks good. Can I start with ₹5,000 and increase later?'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2]
        }
      },
      {
        id: 'adult-sms-1',
        type: 'sms',
        title: 'FASTag Notice',
        content: {
          sender: 'NHAI FASTag',
          message: '[FASTag Notice]\n\nYour FASTag services have been temporarily suspended due to an unpaid toll violation on 27 June.\n\nPay pending dues of ₹87.50 to avoid a ₹500 penalty.\n\nActivate now: fastag-clearance.in/verify\n\n– NHAI FASTag Support'
        },
        options: [
          'Oh I didn\'t know there was a toll issue. I\'ll pay it now and get it cleared.',
          'Click the link and enter upi pin',
          'I\'ll check my FASTag account directly on the NHAI official site or app.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'adult-call-1',
        type: 'call',
        title: 'HDFC Rewards Call',
        content: {
          caller: 'HDFC Bank Rewards',
          message: 'Good evening, I\'m calling from HDFC Bank\'s Reward Points Department.\n\nYour credit card points are about to expire today — they\'re worth ₹3,700.\n\nI can help you redeem them now and convert them to cashback or Amazon vouchers. Just need to verify your card number and expiry date for the process.'
        },
        options: [
          'I\'ll call the HDFC number on my card to verify this. Thanks.',
          'Okay, my card number ends in 1890. Can you process the redemption?',
          'Wait, will I lose the points today if I don\'t redeem now?'
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1]
        }
      },
      {
        id: 'adult-sms-2',
        type: 'sms',
        title: 'Delhivery Package',
        content: {
          sender: 'Delhivery',
          message: '[Delhivery Notice]\n\nYour package could not be delivered due to incomplete address details.\n\nPlease update your address within 24 hrs to avoid return:\n\n👉 www.delhivery-update.info'
        },
        options: [
          'Clicked the link and entered my address. It asked for ₹45 so I paid to avoid issues.',
          'Is this my delhivery order? I\'ll just complete the details to be safe.',
          'I\'ll track my package through courier\'s real website.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'adult-call-2',
        type: 'call',
        title: 'Hospital Emergency',
        content: {
          caller: 'Jeevan Raksha Hospital',
          message: 'Hello?! Is this [your name]?\n\nI\'m calling from Jeevan Raksha Hospital. There\'s been an accident — your younger brother was brought in just now after a bike crash. He\'s conscious but bleeding. We need to start treatment immediately.\n\nWe couldn\'t find his wallet or insurance card. If you can please help with the emergency admission fee — ₹8,500, we can begin the process. UPI or GPay is fine. and please come fast.'
        },
        options: [
          'I understand, but I\'ll call my brother and the hospital directly first. I\'ll get back to you.',
          'Yes, please treat him immediately. I\'m sending the money right now!',
          'Is he badly hurt? Can I send half now and the rest later?'
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1]
        }
      }
    ]
  },
  {
    min: 46,
    max: 60,
    scenarios: [
      {
        id: 'middle-call-1',
        type: 'call',
        title: 'Microsoft Support Call',
        content: {
          caller: 'Microsoft Support',
          message: 'Hello ma\'am, this is Rakesh from Microsoft Support.\n\nWe\'ve detected suspicious login attempts from foreign locations on your computer. Your banking and email could be at risk.\n\nI\'ll help secure your system — please install our support tool so I can assist remotely.'
        },
        options: [
          'Okay, I\'ve installed it. What do I do next?',
          'Okay, but before I give access, can you show me some ID or company email?',
          'Sorry, I don\'t allow remote access. I\'ll check with my local technician.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'middle-sms-1',
        type: 'sms',
        title: 'Netflix Alert',
        content: {
          sender: 'Netflix',
          message: '[Netflix Alert]\n\nYour subscription has been suspended due to failed auto-renewal.\n\nPlease verify your payment info to avoid service interruption:\n\n👉 netflix-support-help.in'
        },
        options: [
          'I\'ll just update my card here.',
          'I\'ll check in the Netflix app.',
          'Maybe it expired — let me check the link.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2]
        }
      },
      {
        id: 'middle-whatsapp-1',
        type: 'whatsapp',
        title: 'Flipkart Prize',
        content: {
          sender: 'Flipkart Rewards',
          message: '🎊 Congratulations! Your number ending in 8742 has won a ₹50,000 Flipkart scratch card from our past festival Mega Sale.\n\nClaim now by verifying your details:\n\n👉 flipkart-prize.in'
        },
        options: [
          'Wow! I did shop last festival. Let me check this.',
          'It says Flipkart — I\'ll just fill the form quickly.',
          'This seems fake. I\'ll check Flipkart\'s app or support page directly.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'middle-sms-2',
        type: 'sms',
        title: 'Housing Survey',
        content: {
          sender: 'District Housing',
          message: '[District Housing Survey]\n\nAs part of the 2024 Urban Property Verification Drive, all homeowners must complete a short survey to update their records.\n\nFailure to respond may result in your name being flagged for incomplete documentation.\n\nComplete the form now:\n\n👉 district-housing-update.in'
        },
        options: [
          'I\'ll check this on the official govt site or call my local office.',
          'Looks official. I\'ll fill it out quickly before the deadline.',
          'Not sure if this is real, but I\'ll do it just in case.'
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1]
        }
      },
      {
        id: 'middle-olx-1',
        type: 'olx',
        title: 'Army Officer Buyer',
        content: {
          buyer: 'Raj (Army)',
          conversation: [
            { sender: 'raj', message: 'Hello sir, I\'m interested in the LG washing machine you listed. Is it still available?' },
            { sender: 'user', message: 'Yes, still available.' },
            { sender: 'raj', message: 'Great, I\'ll take it. I\'m in the army, posted in a restricted zone, so I can\'t come personally.\n\nI\'ll send a pickup agent to collect it, and transfer the full amount before that. Just send your UPI ID. You\'ll get a payment confirmation request — please approve it so the payment goes through.' }
          ]
        },
        options: [
          'Sure, UPI is mohit@okaxis. Let me know once sent.',
          'Okay, I approved the request. When is the pickup coming?',
          'Please send the money directly — I don\'t accept any approval or collect requests.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'middle-sms-3',
        type: 'sms',
        title: 'SBI Rewards',
        content: {
          sender: 'SBI Card',
          message: '[SBI Rewards]\n\nDear customer, your credit card points worth ₹3,250 are expiring today.\n\nRedeem now for cashback or gift vouchers.\n\nCall now to claim: +91 98765 4XXXX'
        },
        options: [
          'I\'ll just confirm my card number when they ask. Should be quick.',
          'Okay, calling now — I don\'t want to lose the points.',
          'Better to check on the SBI Card app. I won\'t trust unknown numbers.'
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [1],
          low_risk: [3]
        }
      }
    ]
  },
  {
    min: 61,
    max: 90,
    scenarios: [
      {
        id: 'senior-whatsapp-1',
        type: 'whatsapp',
        title: 'Ayushman Bharat Card',
        content: {
          sender: 'Ayushman Bharat',
          message: '[Ayushman Bharat Senior Card]\n\nGovt. of India is offering free health checkups & ₹5 lakh coverage for all senior citizens above 60.\n\nTo activate your card, complete KYC today.\n\nCall now: +91 98102 3XXXX'
        },
        options: [
          'This sounds useful — I\'ll call them now and share my Aadhaar.',
          'Only ₹20 for activation? Okay, I\'ll get it done quickly.',
          'I\'ll check this on the official govt website first.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'senior-sms-1',
        type: 'sms',
        title: 'Electricity Board Warning',
        content: {
          sender: 'Electricity Board',
          message: '[Electricity Board]\n\nDear customer, your power connection will be disconnected tonight due to unpaid bill and KYC failure.\n\nTo avoid suspension, call the verification officer immediately:\n\n📞 +91 98765 4XXXX'
        },
        options: [
          'Just to be sure, I\'ll speak to them — I can\'t risk disconnection at night.',
          'They have my meter number? It must be legit. I\'ll sort it out now.',
          'I\'d get it through the electricity board app or bill. I\'ll ignore this.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'senior-whatsapp-2',
        type: 'whatsapp',
        title: 'Government Subsidy',
        content: {
          sender: 'Govt Subsidy Portal',
          message: '[Govt. Subsidy Portal]\n\nDear citizen, your pending subsidy of ₹7,200 under Central Benefit Scheme 2024 is ready for release.\n\nKindly verify your bank details to receive the credit.\n\n👉 subsidy-update-portal.in'
        },
        options: [
          '₹7,200? good scheme. Let me fill it just in case.',
          'I\'ll just verify the account number — they already have my name anyway.',
          'I\'ll ignore this.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'senior-sms-2',
        type: 'sms',
        title: 'PAN-Aadhaar Link',
        content: {
          sender: 'Income Tax Dept',
          message: '[Income Tax Dept]\n\nYour PAN is not linked with Aadhaar. Failure to update will result in a ₹1,000 penalty and PAN deactivation.\n\nLink now to avoid penalties:\n\n👉 pan-uid-update.in'
        },
        options: [
          'I thought I had linked it, but I\'ll do it again to be safe.',
          '₹1,000 fine? I\'ll share the details — just make sure my PAN doesn\'t get blocked.',
          'I\'ll check my PAN-Aadhaar status on the Income Tax portal once.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'senior-whatsapp-3',
        type: 'whatsapp',
        title: 'Senior Yatra Scheme',
        content: {
          sender: 'Govt Senior Yatra',
          message: '[Govt Approved Senior Yatra Scheme]\n\nGet a 5-day Char Dham tour for senior citizens at just ₹999, including travel, meals & lodging.\n\nLimited slots available. Book now through the Seva Portal:\n\n👉 senior-yatra-help.in'
        },
        options: [
          'Such a low price! Let me register before all slots are gone.',
          'Looks government approved. I\'ll fill the form quickly.',
          'I\'ll check with the official tourism site or ask my children first.'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      },
      {
        id: 'senior-call-1',
        type: 'call',
        title: 'Grandson Accident Call',
        content: {
          caller: 'City Hospital',
          message: 'Hello ji, I\'m calling from City Hospital. Your grandson Rohan has been in an accident. He\'s conscious, but we need to begin treatment immediately. There\'s no time — please send ₹15,000 right away for admission formalities.'
        },
        options: [
          'Oh no! Please send the payment details — I\'ll transfer right away.',
          'Is he okay? I\'ll send the money, just take care of him please.',
          'I\'ll first call his parents and confirm. What\'s your full name and hospital number?'
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3]
        }
      }
    ]
  }
];