import React, { useState } from "react";
import { User } from "../types";
import {
  User as UserIcon,
  Calendar,
  Globe,
  Sparkles,
  Shield,
} from "lucide-react";
import { getTranslation, getLanguageOptions } from "../utils/language";

interface InitialFormProps {
  onSubmit: (user: User) => void;
  playClickSound: () => void;
}

const InitialForm: React.FC<InitialFormProps> = ({
  onSubmit,
  playClickSound,
}) => {
  const [formData, setFormData] = useState({
    name: "",
    age: "",
    language: "english" as User["language"],
  });
  const [ageError, setAgeError] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setAgeError("");

    if (formData.name && formData.age) {
      const age = parseInt(formData.age);

      // Check age restrictions
      if (age < 13) {
        setAgeError("Sorry, this assessment is only available for ages 13-17.");
        return;
      }

      if (age > 17) {
        setAgeError(
          "Sorry, this assessment is designed specifically for teens aged 13-17 only."
        );
        return;
      }

      playClickSound();
      onSubmit({
        name: formData.name,
        age: age,
        language: formData.language,
      });
    }
  };

  const t = (key: string) => getTranslation(formData.language, key);
  const languageOptions = getLanguageOptions(formData.language);

  return (
    <div className="min-h-screen flex items-center justify-center p-4 relative overflow-hidden">
      <div className="w-full max-w-md relative z-10">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-green-400 via-green-300 to-green-200 bg-clip-text text-transparent mb-3 font-mono">
            {t("The Cyber Mission")}
          </h1>
          <p className="text-green-300 text-lg font-mono">
            {t("The internet’s watching—can you outplay it?")}
          </p>
          <div className="flex items-center justify-center gap-2 mt-4 text-green-400/70">
            <Sparkles className="w-4 h-4" />
            <span className="text-sm font-mono">
              {t("interactive")} • {t("personalized")} • {t("secure")}
            </span>
            <Sparkles className="w-4 h-4" />
          </div>
        </div>

        <form
          onSubmit={handleSubmit}
          className="bg-black/60 backdrop-blur-xl rounded-3xl p-8 border border-green-500/30 shadow-2xl shadow-green-500/20"
        >
          <div className="space-y-6">
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-green-300 mb-3 font-mono">
                <UserIcon className="w-4 h-4 text-green-400" />
                {t("fullName")}
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => {
                  playClickSound();
                  setFormData({ ...formData, name: e.target.value });
                }}
                className="w-full px-4 py-4 bg-black/40 border border-green-500/30 rounded-xl text-green-300 placeholder-green-400/50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-400 transition-all duration-200 backdrop-blur-sm font-mono"
                placeholder={t("enterFullName")}
                required
              />
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-green-300 mb-3 font-mono">
                <Calendar className="w-4 h-4 text-green-400" />
                {t("age")}{" "}
                <span className="text-green-400/70">(13-17 only)</span>
              </label>
              <input
                type="number"
                min="13"
                max="17"
                value={formData.age}
                onChange={(e) => {
                  playClickSound();
                  setFormData({ ...formData, age: e.target.value });
                  setAgeError(""); // Clear error when user types
                }}
                className={`w-full px-4 py-4 bg-black/40 border rounded-xl text-green-300 placeholder-green-400/50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-400 transition-all duration-200 backdrop-blur-sm font-mono ${
                  ageError ? "border-red-500/50" : "border-green-500/30"
                }`}
                placeholder={t("enterAge")}
                required
              />
              {ageError && (
                <p className="mt-2 text-red-400 text-sm font-mono flex items-center gap-2">
                  <span>⚠️</span>
                  {ageError}
                </p>
              )}
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-green-300 mb-3 font-mono">
                <Globe className="w-4 h-4 text-green-400" />
                {t("preferredLanguage")}
              </label>
              <select
                value={formData.language}
                onChange={(e) => {
                  playClickSound();
                  setFormData({
                    ...formData,
                    language: e.target.value as User["language"],
                  });
                }}
                className="w-full px-4 py-4 bg-black/40 border border-green-500/30 rounded-xl text-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-400 transition-all duration-200 backdrop-blur-sm font-mono"
              >
                <option value="english">{languageOptions.english}</option>
                <option value="hindi">{languageOptions.hindi}</option>
                <option value="urdu">{languageOptions.urdu}</option>
                <option value="telugu">{languageOptions.telugu}</option>
              </select>
            </div>
          </div>

          <button
            type="submit"
            className="w-full mt-8 px-6 py-4 bg-gradient-to-r from-green-600 to-green-700 text-black font-semibold rounded-xl hover:from-green-500 hover:to-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-black transition-all duration-200 transform hover:scale-[1.02] shadow-xl shadow-green-500/25 font-mono"
          >
            <div className="flex items-center justify-center gap-2">
              <Shield className="w-5 h-5" />
              {t("beginAssessment")}
            </div>
          </button>
        </form>
      </div>
    </div>
  );
};

export default InitialForm;
