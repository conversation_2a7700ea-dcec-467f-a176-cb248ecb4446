import React from 'react';
import { <PERSON><PERSON><PERSON> } from '../../types';
import { Mail, Star, Archive, Trash2, Reply } from 'lucide-react';

interface EmailSimulationProps {
  scenario: Scenario;
  onResponse: (optionIndex: number) => void;
}

const EmailSimulation: React.FC<EmailSimulationProps> = ({ scenario, onResponse }) => {
  const content = scenario.content as { from: string; subject: string; message: string };

  return (
    <div className="max-w-2xl mx-auto">
      {/* Email Interface */}
      <div className="bg-slate-800 rounded-lg overflow-hidden shadow-2xl">
        {/* Email Header */}
        <div className="bg-slate-900 px-6 py-4 border-b border-slate-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Mail className="w-5 h-5 text-blue-400" />
              <h3 className="text-white font-medium">Inbox</h3>
            </div>
            <div className="flex gap-2">
              <Star className="w-5 h-5 text-slate-400 hover:text-yellow-400 cursor-pointer" />
              <Archive className="w-5 h-5 text-slate-400 hover:text-blue-400 cursor-pointer" />
              <Trash2 className="w-5 h-5 text-slate-400 hover:text-red-400 cursor-pointer" />
            </div>
          </div>
        </div>

        {/* Email Content */}
        <div className="p-6">
          {/* Email Subject */}
          <h2 className="text-xl font-semibold text-white mb-4">{content.subject}</h2>
          
          {/* Sender Info */}
          <div className="flex items-center gap-3 mb-6 pb-4 border-b border-slate-700">
            <div className="w-10 h-10 bg-slate-600 rounded-full flex items-center justify-center">
              <span className="text-slate-200 font-semibold">
                {content.from.split('@')[0].charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <p className="text-white font-medium">{content.from}</p>
              <p className="text-slate-400 text-sm">to: you</p>
            </div>
            <div className="ml-auto text-slate-400 text-sm">
              Today, 9:30 AM
            </div>
          </div>

          {/* Email Body */}
          <div className="prose prose-invert max-w-none">
            <p className="text-slate-200 leading-relaxed whitespace-pre-line">
              {content.message}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 mt-6 pt-4 border-t border-slate-700">
            <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <Reply className="w-4 h-4" />
              Reply
            </button>
            <button className="px-4 py-2 bg-slate-700 text-slate-200 rounded-lg hover:bg-slate-600 transition-colors">
              Forward
            </button>
          </div>
        </div>
      </div>

      {/* Response Options */}
      <div className="mt-8 space-y-3">
        <h4 className="text-white font-medium mb-4">How would you respond?</h4>
        {scenario.options.map((option, index) => (
          <button
            key={index}
            onClick={() => onResponse(index + 1)}
            className="w-full p-4 text-left bg-slate-800/50 border border-slate-700 rounded-lg text-slate-200 hover:bg-slate-700/50 hover:border-slate-600 transition-all duration-200"
          >
            <span className="text-blue-400 font-medium">{index + 1}. </span>
            {option}
          </button>
        ))}
      </div>
    </div>
  );
};

export default EmailSimulation;