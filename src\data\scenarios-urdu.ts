import { AgeGroup } from "../types";

export const ageGroupsUrdu: AgeGroup[] = [
  {
    min: 10,
    max: 17,
    scenarios: [
      {
        id: "teen-sms-1",
        type: "sms",
        title: "InstaPay اطلاع",
        content: {
          sender: "InstaPay اطلاع",
          message:
            'ہیلو! آپ کا ₹500 کا collab بونس "reel challenge" سے تیار ہے۔\n\nUPI کے ذریعے قبول کرنے کے لیے یہاں ٹیپ کریں: [reels-payout.in]\n\n– Instagram Rewards ✨',
        },
        options: [
          "میں نے لنک پر کلک کیا اور اپنا UPI PIN ڈالا۔ ₹500 کب آئیں گے؟",
          "ارے، میں نے تو کوئی challenge میں حصہ ہی نہیں لیا۔ یہ سچ ہے یا کوئی غلطی؟",
          "میں Instagram کی آفیشل ایپ پر چیک کروں گا۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "teen-snapchat-1",
        type: "snapchat",
        title: "Snapchat چیٹ",
        content: {
          messages: [
            {
              sender: "تنیشق",
              message:
                "ہیلو، تھوڑا random ہے I know 😅 لیکن آپ Quick Add میں آئے اور لگتا ہے کہ ہمارے کچھ mutuals ہیں؟\n\nآپ کچھ جانے پہچانے لگ رہے تھے — کیا آپ [عام اسکول کا نام] میں پڑھتے ہیں یا [مقامی مقام] کے قریب رہتے ہیں؟",
            },
          ],
        },
        options: [
          "ہاں، میں وہیں سے ہوں۔",
          "میسج کو نظر انداز کر دیں۔",
          "پہلے ثابت کرو کہ تم اصلی ہو 😛 کوئی سیلفی بھیجو یا کچھ اور۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2],
        },
      },
      {
        id: "teen-instagram-post-1",
        type: "instagram_post",
        title: "Instagram پوسٹ",
        content: {
          account: "@trendie_clips_xo",
          caption:
            "ہیلو! ہم trendy ایکسیسریز (rings, clips, charms وغیرہ) کا پرانا اسٹاک کلیئر کر رہے ہیں — سب کچھ ₹99 سے کم میں، اس ہفتے مفت ڈیلیوری 🎀\n\nبس ہمیں DM کریں اور UPI سے پیمنٹ کریں۔ پہلے 20 خریداروں کو ایک فری گفٹ بھی ملے گا 💕\n\nTagged پوسٹس میں ریویوز چیک کریں 💬\n– @trendie_clips_xo 💖",
          likes: "1,247",
          time: "2h",
        },
        options: [
          "OMG، یہ تو بہت سستا ہے!! مجھے 3 رنگز اور ایک ہیئر کلپ چاہیے۔ UPI ID؟",
          "کیا میں ابھی پیمنٹ کا اسکرین شاٹ بھیج کر اگلے ہفتے ڈیلیوری لے سکتا ہوں؟",
          "کیا آپ کی ویب سائٹ ہے یا Insta Checkout لنک؟ اور آپ کہاں سے شپ کرتے ہیں؟",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "teen-whatsapp-1",
        type: "whatsapp",
        title: "WhatsApp چیٹ",
        content: {
          sender: "EdInsights Survey",
          message:
            "🎉 ہیلو! آپ کو EdInsights India کی جانب سے ایک خصوصی اسٹوڈنٹ ریوارڈ سروے کے لیے منتخب کیا گیا ہے۔\n\n2 منٹ کا کوئز مکمل کریں اور فوراً ₹500 کے مفت Amazon vouchers حاصل کریں! 🛍\n\nصرف محدود انٹریز۔ ابھی شروع کریں: [ed-insights-survey.in]\n\n– 5000+ اسٹوڈنٹس کی طرف سے قابلِ اعتماد ✅",
        },
        options: [
          "زبردست لگ رہا ہے! ابھی فل کرتا ہوں، امید ہے اصلی ہوگا lol",
          "کیا یہ کسی آفیشل ویب سائٹ سے ہے؟ EdInsights India کا کبھی نام نہیں سنا۔",
          "کیا فون نمبر دینا ضروری ہے؟ میں نے کوئز شروع کر دیا ہے۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2],
        },
      },
      {
        id: "teen-instagram-2",
        type: "instagram",
        title: "Instagram DM",
        content: {
          sender: "@workfromhome_students24",
          message:
            "📢 HIRING: طلبا کے لیے Part-time جاب (گھر سے کام)\n✅ صرف 1–2 گھنٹے روزانہ\n💰 ہفتہ وار ₹5,000–₹12,000 کمائیں\n💼 کوئی اسکلز درکار نہیں، صرف فون اور انٹرنیٹ کافی ہے\n\nاگر دلچسپی ہے تو DM کریں 'JOB' اور ہم مکمل تفصیل بھیجیں گے۔\n– @workfromhome_students24",
        },
        options: [
          "JOB! کام کیا ہے؟ مجھے ابھی سائیڈ ہسل کی ضرورت ہے۔",
          "شروع کرنے کے لیے کیا مجھے کچھ ادا کرنا ہوگا؟ میں بعد میں بندوبست کر سکتا ہوں۔",
          "کیا آپ اپنی آفیشل ویب سائٹ یا کمپنی رجسٹریشن شیئر کر سکتے ہیں؟",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "teen-whatsapp-2",
        type: "whatsapp",
        title: "WhatsApp گروپ چیٹ",
        content: {
          sender: "گروپ ممبر",
          message:
            "OMG مجھے ابھی 1700 مفت Robux اس سائٹ سے ملے!!\n\nکوئی سروے یا پاسورڈ نہیں چاہیے — صرف اپنا یوزر نیم ڈالیں اور جتنے Robux چاہئیں، منتخب کریں\n\n100% کام کرتا ہے، میں نے پہلے ہی limiteds خرید لیے\n\nپیچھے نہ رہنا: [robux-fast.com]\n\nاگر کام کرے تو بتانا!!",
        },
        options: [
          "یہ لنک مشکوک لگ رہا ہے۔ Roblox کبھی فری Robux نہیں دیتا ایسے۔",
          "BRO اگر یہ واقعی کام کرتا ہے تو میں ابھی core blocks خریدنے جا رہا ہوں 😂",
          "کیا یہ وہ نیا glitch ہے؟؟ ابھی چیک کرتا ہوں۔",
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1],
        },
      },
    ],
  },
  {
    min: 18,
    max: 30,
    scenarios: [
      {
        id: "young-email-1",
        type: "email",
        title: "Microsoft سیکیورٹی الرٹ",
        content: {
          from: "<EMAIL>",
          subject: "⚠️ اکاؤنٹ سیکیورٹی الرٹ – فوری کارروائی درکار ہے",
          message:
            "محترم صارف،\n\nہمیں آپ کے Microsoft اکاؤنٹ پر ممبئی، انڈیا اور ٹورنٹو، کینیڈا سے متعدد غیر مجاز لاگ ان کی کوششیں ملی ہیں۔\n\nآپ کے اکاؤنٹ کی حفاظت کے لیے ہم نے عارضی طور پر رسائی بند کر دی ہے۔\n\n✅ براہ کرم اپنی شناخت کی فوری تصدیق کریں اس محفوظ پورٹل پر:\n\n[Verify Now] → https://msverify-center.info/accountcheck\n\nاگر آپ نے 24 گھنٹوں کے اندر کارروائی نہ کی تو آپ کا اکاؤنٹ مستقل طور پر معطل کر دیا جائے گا۔\n\nشکریہ،\nMicrosoft سیکیورٹی ٹیم 🔒",
        },
        options: [
          "ارے نہیں! ابھی تصدیق کرتا ہوں۔ (لنک پر کلک کر کے تفصیلات داخل کرتا ہے)",
          "کون سی لاگ ان کوششیں؟ میں نے ان جگہوں سے لاگ ان نہیں کیا! (ای میل کا جواب دیتا ہے)",
          "پہلے Microsoft اکاؤنٹ چیک کر لیتا ہوں۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "young-linkedin-1",
        type: "linkedin",
        title: "LinkedIn جاب کی پیشکش",
        content: {
          sender: "پریا مہرا، HR Assistant، Wipro Global IT Solutions",
          message:
            "ہیلو [آپ کا نام],\n\nمیں پریا مہرا ہوں، HR Assistant، Wipro Global IT Solutions سے۔ ہم نے آپ کا پروفائل دیکھا اور ہمیں لگتا ہے کہ آپ ہمارے ریموٹ انٹرن شپ پروگرام کے لیے بہترین امیدوار ہیں۔\n\n✔️ لچکدار اوقات\n✔️ ₹18,000 ماہانہ وظیفہ\n✔️ گھر سے کام\n\nبس یہ مختصر فارم بھریں: [wipro-careers-apply.in]\nمحدود نشستیں ہیں، جلدی کریں۔ رجسٹریشن کے بعد آپ کو آن بورڈنگ اسٹیپس مل جائیں گے۔",
        },
        options: [
          "شکریہ، کیا یہ Wipro کی آفیشل ویب سائٹ یا کیریئرز پیج پر بھی ہے؟ میں وہیں سے اپلائی کرنا چاہوں گا۔",
          "بہت زبردست! فارم بھر دیا ہے اور اپنے ID ڈاکیومنٹس بھیج دیے ہیں۔ امید ہے سلیکٹ ہو جاؤں!",
          "کیا کوئی پراسیسنگ فیس دینی ہے؟ یا آن بورڈنگ انسٹرکشنز کا انتظار کرنا چاہیے؟",
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1],
        },
      },
      {
        id: "young-olx-1",
        type: "olx",
        title: "OLX خریدار چیٹ",
        content: {
          buyer: "Army Personnel",
          message:
            "ہیلو، میں نے آپ کی لسٹنگ دیکھی [مثال: iPhone 12] کی۔ مجھے دلچسپی ہے اور میں فوراً ادائیگی کر سکتا ہوں۔\n\nمیں انڈین آرمی میں ہوں، اس لیے ملاقات ممکن نہیں۔ میرا ایجنٹ آپ کے ایڈریس پر آئے گا۔\n\nمیں UPI کے ذریعے پیشگی ادائیگی کر دوں گا — بس اپنا UPI ID شیئر کریں۔ آپ کو جلد ایک پیمنٹ لنک ملے گا، اسے اپروو کر دیں۔",
        },
        options: [
          "جی، یہ رہا میرا UPI ID۔ جیسے ہی پیمنٹ آئے گی کنفرم کر دوں گا۔",
          "اچھا، مجھے پیمنٹ ریکویسٹ لنک ملا ہے۔ ابھی اپروو کر رہا ہوں۔",
          "معذرت، میں انجان خریداروں سے لنک یا UPI ایڈوانس قبول نہیں کرتا۔ صرف نقد یا COD۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "young-snapchat-1",
        type: "snapchat",
        title: "Snapchat اجنبی",
        content: {
          messages: [
            {
              sender: "اجنبی",
              message:
                "ہیلو! یقین نہیں کہ پہلے بات ہوئی یا نہیں، مگر تمہارا Bitmoji کچھ جانا پہچانا لگا 😅\n\nتم تھوڑے جانے پہچانے لگتے ہو… کیا ہم [کسی مشہور کالج فیسٹ یا کیفے] پر ملے تھے؟ یا شاید Insta یا Quick Add پر؟ haha پتا نہیں 😄",
            },
          ],
        },
        options: [
          "کافی رینڈم لگا… اگر کبھی نہیں ملے تو بات نہ ہی کریں بہتر ہے۔",
          "میسج کو نظر انداز کرتا ہوں۔",
          "Haha تم بھی جانے پہچانے لگ رہے ہو۔ تمہارا Insta کیا ہے؟",
        ],
        riskLevels: {
          high_risk: [3],
          medium_risk: [1],
          low_risk: [2],
        },
      },
      {
        id: "young-call-1",
        type: "call",
        title: "ایمرجنسی کال",
        content: {
          caller: "Hospital Emergency",
          message:
            "ہیلو؟ کیا یہ [آپ کا نام] ہے؟\n\nمیں [ہسپتال کا نام] سے بول رہا ہوں — آپ کے بھائی کا ایکسیڈنٹ ہو گیا ہے۔ وہ ہوش میں ہیں مگر ہمیں فوری علاج شروع کرنا ہے۔\n\nہمارے پاس ان کی مکمل ID یا انشورنس نہیں ہے۔ براہ کرم فوراً ₹10,000 UPI کے ذریعے اس نمبر پر بھیج دیں۔ وقت بہت اہم ہے۔",
        },
        options: [
          "کیا ہوا؟! ابھی بھیجتا ہوں — پلیز ان کا خیال رکھیں!",
          "میرے پاس ابھی 10k نہیں ہیں، آدھے ابھی بھیج دوں؟ باقی بعد میں؟",
          "یہ سنجیدہ لگ رہا ہے، لیکن میں پہلے اپنے بھائی اور ہسپتال کو خود کال کر کے تصدیق کروں گا۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "young-email-2",
        type: "email",
        title: "Accenture انٹرن شپ",
        content: {
          from: "<EMAIL>",
          subject: "Accenture ریموٹ انٹرن شپ کی پیشکش",
          message:
            "ہیلو [آپ کا نام],\n\nآپ کو Accenture کے پارٹنر پروگرام کے تحت ریموٹ انٹرن شپ کے لیے منتخب کیا گیا ہے۔\n\n🧠 رول: ڈیجیٹل انٹرن\n🕒 دورانیہ: 1 مہینہ\n💸 وظیفہ: ₹15,000\n\nاپنی جگہ کنفرم کرنے کے لیے براہ کرم 24 گھنٹوں کے اندر یہ فارم مکمل کریں:\n\n👉 www.accenture-internform.live\n\nشکریہ،\nسنیہا ورما\nHR ٹیم – Accenture پارٹنر نیٹ ورک",
        },
        options: [
          "بہت شکریہ! فارم جمع کروا دیا ہے، ID اور ریزیومے بھی شیئر کر دیے ہیں۔ آفر لیٹر کا انتظار ہے!",
          "شکریہ، لیکن کیا یہ انٹرن شپ Accenture کی آفیشل ویب سائٹ پر موجود ہے؟ میں تصدیق شدہ چینلز سے اپلائی کرنا پسند کرتا ہوں۔",
          "ہیلو، بس چیک کر رہا ہوں — کیا وظیفہ کنفرم ہے؟ اور ویلکم کٹ کب ملے گی؟",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2],
        },
      },
    ],
  },
  {
    min: 31,
    max: 45,
    scenarios: [
      {
        id: "adult-olx-1",
        type: "olx",
        title: "OLX واشنگ مشین سیل",
        content: {
          buyer: "ریلوکیٹ ہونے والا فروخت کنندہ",
          conversation: [
            {
              sender: "user",
              message: "ہیلو، کیا LG واشنگ مشین ابھی دستیاب ہے؟",
            },
            {
              sender: "seller",
              message:
                "جی سر، بالکل صحیح حالت میں ہے۔ صرف ₹8,500 میں۔ ہم شفٹ ہو رہے ہیں، اس لیے جلدی بیچنی ہے۔",
            },
            { sender: "user", message: "کیا میں آکر دیکھ سکتا ہوں؟" },
            {
              sender: "seller",
              message:
                "یہ پہلے ہی پیک ہو چکی ہے۔ میں آپ کو سیدھا ڈیلیور کر دوں گا۔ صرف ₹300 بطور ٹوکن بھیج دیں تاکہ میں آپ کے لیے رکھ سکوں۔",
            },
          ],
        },
        options: [
          "ٹھیک ہے، میں ابھی ₹300 بھیج رہا ہوں۔ شکریہ۔",
          "ٹھیک ہے، میں ٹوکن بھیج رہا ہوں۔ بس آج ہی ڈیلیوری کروانا پلیز۔",
          "سمجھ گیا، لیکن میں ایڈوانس میں کچھ نہیں دیتا۔ کیا پہلے دیکھ سکتا ہوں؟",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "adult-whatsapp-1",
        type: "whatsapp",
        title: "انویسٹمنٹ کا موقع",
        content: {
          sender: "CredGrow Capital",
          message:
            "گڈ آفٹر نون سر، میں CredGrow Capital کی طرف سے بات کر رہا ہوں، جو SEBI سے منظور شدہ کمپنی ہے۔ ہم شارٹ ٹرم فکسڈ ریٹرن پلان پیش کر رہے ہیں۔\n\n✅ صرف 15 دن میں 25–30% منافع\n✅ سرمایہ محفوظ\n✅ ٹیکس فری رقم نکالنے کی سہولت\n\nIT اور فنانس فیلڈ کے کئی پروفیشنلز پہلے ہی شامل ہو چکے ہیں۔ اس ہفتے مزید 10 کلائنٹس کو آن بورڈ کر رہے ہیں۔\n\nمیں بروشر اور آج کے لائیو پے آؤٹ اسکرین شاٹس بھیج سکتا ہوں۔ اگر آپ جلدی کال پر بات کرنا چاہیں تو بتائیں۔",
        },
        options: [
          "اچھی آفر لگ رہی ہے، پلیز پلان کی تفصیلات اور پیمنٹ طریقہ بھیجیں۔",
          "میں پہلے آپ کی SEBI رجسٹریشن اور ویب سائٹ چیک کرنا چاہوں گا، کیا شیئر کر سکتے ہیں؟",
          "ٹھیک لگ رہا ہے، کیا میں ₹5,000 سے شروع کر سکتا ہوں اور بعد میں بڑھا سکتا ہوں؟",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2],
        },
      },
      {
        id: "adult-sms-1",
        type: "sms",
        title: "FASTag نوٹس",
        content: {
          sender: "NHAI FASTag",
          message:
            "[FASTag اطلاع]\n\nآپ کی FASTag سروسز 27 جون کے ایک غیر ادا شدہ ٹول کی وجہ سے عارضی طور پر معطل کر دی گئی ہیں۔\n\n₹87.50 کی بقایا رقم ابھی ادا کریں تاکہ ₹500 جرمانے سے بچا جا سکے۔\n\nابھی ایکٹیویٹ کریں: fastag-clearance.in/verify\n\n– NHAI FASTag سپورٹ",
        },
        options: [
          "اوہ، مجھے معلوم ہی نہیں تھا کوئی ٹول بقایا ہے۔ ابھی ادا کرتا ہوں۔",
          "لنک پر کلک کریں اور UPI PIN درج کریں۔",
          "میں اپنے FASTag اکاؤنٹ کو NHAI کی آفیشل سائٹ یا ایپ پر چیک کروں گا۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "adult-call-1",
        type: "call",
        title: "HDFC ریوارڈز کال",
        content: {
          caller: "HDFC Bank ریوارڈز",
          message:
            "گڈ ایوننگ، میں HDFC بینک کے Reward Points ڈیپارٹمنٹ سے بول رہا ہوں۔\n\nآپ کے کریڈٹ کارڈ کے پوائنٹس آج ختم ہو رہے ہیں — ان کی قیمت ₹3,700 ہے۔\n\nمیں آپ کی مدد کر سکتا ہوں کہ انہیں کیش بیک یا Amazon واوچرز میں تبدیل کر لیں۔ اس کے لیے صرف آپ کے کارڈ نمبر اور ایکسپائری ڈیٹ کی تصدیق کرنی ہوگی۔",
        },
        options: [
          "میں HDFC کے کارڈ پر دیے گئے نمبر پر کال کر کے تصدیق کروں گا۔ شکریہ۔",
          "ٹھیک ہے، میرے کارڈ کا آخری نمبر 1890 ہے۔ کیا آپ پوائنٹس ریڈیم کر سکتے ہیں؟",
          "کیا واقعی آج پوائنٹس ختم ہو جائیں گے اگر میں نے ابھی ریڈیم نہ کیے؟",
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1],
        },
      },
      {
        id: "adult-sms-2",
        type: "sms",
        title: "Delhivery پیکیج",
        content: {
          sender: "Delhivery",
          message:
            "[Delhivery اطلاع]\n\nآپ کے پیکج کی ڈیلیوری نامکمل ایڈریس کی وجہ سے نہیں ہو سکی۔\n\nبراہ کرم 24 گھنٹوں کے اندر ایڈریس اپڈیٹ کریں تاکہ ریٹرن سے بچا جا سکے:\n\n👉 www.delhivery-update.info",
        },
        options: [
          "میں نے لنک پر کلک کر کے ایڈریس ڈالا، ₹45 مانگا تو میں نے دے دیا تاکہ مسئلہ نہ ہو۔",
          "کیا یہ میری Delhivery آرڈر ہے؟ میں تفصیلات مکمل کر دیتا ہوں، سیفٹی کے لیے۔",
          "میں کورئیر کی اصلی ویب سائٹ سے اپنا پیکج ٹریک کروں گا۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "adult-call-2",
        type: "call",
        title: "ہسپتال کی ایمرجنسی",
        content: {
          caller: "Jeevan Raksha Hospital",
          message:
            "ہیلو! کیا یہ [آپ کا نام] ہے؟\n\nمیں Jeevan Raksha Hospital سے بات کر رہا ہوں۔ ایک حادثہ ہوا ہے — آپ کے چھوٹے بھائی کو ابھی ابھی بائیک ایکسیڈنٹ کے بعد لایا گیا ہے۔ وہ ہوش میں ہے مگر خون بہہ رہا ہے۔ فوری علاج شروع کرنا ہے۔\n\nہمیں اس کا والٹ یا انشورنس کارڈ نہیں ملا۔ اگر آپ براہ کرم ایمرجنسی ایڈمیشن فیس ₹8,500 بھیج سکیں تو ہم علاج شروع کر سکتے ہیں۔ UPI یا GPay چلے گا، اور براہ کرم جلدی آئیں۔",
        },
        options: [
          "میں سمجھ گیا، لیکن میں پہلے اپنے بھائی اور اسپتال سے خود کال کر کے تصدیق کروں گا۔ پھر جواب دوں گا۔",
          "جی، براہ کرم فوری علاج کریں۔ میں ابھی پیسے بھیج رہا ہوں!",
          "کیا وہ بہت زخمی ہے؟ کیا میں آدھی رقم ابھی اور باقی بعد میں بھیج سکتا ہوں؟",
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1],
        },
      },
    ],
  },
  {
    min: 46,
    max: 60,
    scenarios: [
      {
        id: "middle-call-1",
        type: "call",
        title: "Microsoft سپورٹ کال",
        content: {
          caller: "Microsoft Support",
          message:
            "ہیلو میڈم، میں راکیش بول رہا ہوں Microsoft Support سے۔\n\nہم نے آپ کے کمپیوٹر پر غیر ملکی مقامات سے مشتبہ لاگ ان کی کوششیں دیکھی ہیں۔ آپ کا بینکنگ اور ای میل اکاؤنٹ خطرے میں ہو سکتا ہے۔\n\nمیں آپ کے سسٹم کو محفوظ بنانے میں مدد کروں گا — براہ کرم ہمارا سپورٹ ٹول انسٹال کریں تاکہ میں ریموٹلی مدد کر سکوں۔",
        },
        options: [
          "ٹھیک ہے، میں نے انسٹال کر لیا۔ اب کیا کرنا ہے؟",
          "ٹھیک ہے، لیکن ایکسس دینے سے پہلے کیا آپ اپنی ID یا کمپنی کا ای میل دکھا سکتے ہیں؟",
          "معذرت، میں ریموٹ ایکسس کی اجازت نہیں دیتا۔ میں اپنے مقامی ٹیکنیشن سے چیک کر لوں گا۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "middle-sms-1",
        type: "sms",
        title: "Netflix الرٹ",
        content: {
          sender: "Netflix",
          message:
            "[Netflix Alert]\n\nآپ کی سبسکرپشن آٹو-رینیول فیل ہونے کی وجہ سے معطل کر دی گئی ہے۔\n\nبراہ کرم سروس میں خلل سے بچنے کے لیے اپنی پیمنٹ معلومات کی تصدیق کریں:\n\n👉 netflix-support-help.in",
        },
        options: [
          "میں بس یہاں کارڈ اپڈیٹ کر دیتا ہوں۔",
          "میں Netflix ایپ میں چیک کر لوں گا۔",
          "شاید ایکسپائر ہو گئی ہو — لنک چیک کر لوں؟",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2],
        },
      },
      {
        id: "middle-whatsapp-1",
        type: "whatsapp",
        title: "Flipkart گفٹ",
        content: {
          sender: "Flipkart ریوارڈز",
          message:
            "🎊 مبارک ہو! آپ کا نمبر 8742 پر ختم ہونے والا ₹50,000 کا Flipkart اسکریچ کارڈ جیت گیا ہے ہماری پچھلی فیسٹیول Mega Sale میں۔\n\nابھی کلیم کریں:\n\n👉 flipkart-prize.in",
        },
        options: [
          "واہ! میں نے واقعی پچھلے فیسٹیول میں شاپنگ کی تھی۔ ذرا چیک کرتا ہوں۔",
          "یہ تو Flipkart ہی لگ رہا ہے — فارم جلدی سے بھر دیتا ہوں۔",
          "یہ نقلی لگ رہا ہے۔ میں Flipkart کی ایپ یا سپورٹ پیج پر چیک کر لوں گا۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "middle-sms-2",
        type: "sms",
        title: "گھریلو سروے",
        content: {
          sender: "ضلعی ہاؤسنگ",
          message:
            "[District Housing Survey]\n\n2024 اربن پراپرٹی ویریفیکیشن ڈرائیو کے تحت تمام ہوم اونرز کو اپنا ریکارڈ اپڈیٹ کرنے کے لیے ایک مختصر سروے مکمل کرنا ضروری ہے۔\n\nجواب نہ دینے کی صورت میں آپ کا نام نامکمل دستاویزات کے لیے فلیگ کیا جا سکتا ہے۔\n\nابھی فارم مکمل کریں:\n\n👉 district-housing-update.in",
        },
        options: [
          "میں یہ آفیشل گورنمنٹ ویب سائٹ یا مقامی دفتر سے چیک کر لوں گا۔",
          "کافی آفیشل لگ رہا ہے۔ ڈیڈ لائن سے پہلے بھر دیتا ہوں۔",
          "پتہ نہیں اصلی ہے یا نہیں، پھر بھی احتیاطاً کر دیتا ہوں۔",
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1],
        },
      },
      {
        id: "middle-olx-1",
        type: "olx",
        title: "آرمی آفیسر خریدار",
        content: {
          buyer: "راج (آرمی)",
          conversation: [
            {
              sender: "raj",
              message:
                "ہیلو سر، مجھے آپ کی LG واشنگ مشین میں دلچسپی ہے۔ کیا ابھی بھی دستیاب ہے؟",
            },
            { sender: "user", message: "جی، ابھی دستیاب ہے۔" },
            {
              sender: "raj",
              message:
                "زبردست، میں لے لیتا ہوں۔ میں آرمی میں ہوں اور محدود علاقے میں تعینات ہوں، اس لیے خود نہیں آ سکتا۔\n\nمیں ایک پک اپ ایجنٹ بھیج دوں گا، اور اس سے پہلے پوری رقم ٹرانسفر کر دوں گا۔ بس اپنا UPI ID دے دیں۔ آپ کو ایک پیمنٹ کنفرمیشن ریکویسٹ ملے گی — براہ کرم اسے منظور کریں تاکہ ادائیگی مکمل ہو جائے۔",
            },
          ],
        },
        options: [
          "ٹھیک ہے، میرا UPI ہے mohit@okaxis۔ بھیجنے کے بعد بتانا۔",
          "اوکے، میں نے ریکویسٹ اپروو کر دی۔ پک اپ کب آئے گا؟",
          "براہ کرم پیمنٹ سیدھی بھیجیں — میں کسی بھی ریکویسٹ یا کلیکشن لنک کو قبول نہیں کرتا۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "middle-sms-3",
        type: "sms",
        title: "SBI ریوارڈز",
        content: {
          sender: "SBI Card",
          message:
            "[SBI Rewards]\n\nمحترم صارف، آپ کے کریڈٹ کارڈ پوائنٹس جن کی مالیت ₹3,250 ہے، آج ایکسپائر ہو رہے ہیں۔\n\nابھی کلیم کریں — کیش بیک یا گفٹ واؤچر حاصل کریں۔\n\nکال کریں: +91 98765 4XXXX",
        },
        options: [
          "بس کال کے دوران اپنا کارڈ نمبر کنفرم کر دیتا ہوں۔ جلدی کام ہو جائے گا۔",
          "ابھی کال کرتا ہوں — پوائنٹس ضائع نہیں ہونے چاہییں۔",
          "بہتر ہے SBI کارڈ ایپ پر چیک کر لوں۔ نامعلوم نمبر پر یقین نہیں کرتا۔",
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [1],
          low_risk: [3],
        },
      },
    ],
  },
  {
    min: 61,
    max: 90,
    scenarios: [
      {
        id: "senior-whatsapp-1",
        type: "whatsapp",
        title: "آیوشمان بھارت کارڈ",
        content: {
          sender: "آیوشمان بھارت",
          message:
            "[آیوشمان بھارت سینئر کارڈ]\n\nحکومتِ ہند کی جانب سے 60 سال سے زائد عمر کے سینئر شہریوں کے لیے مفت ہیلتھ چیک اپس اور ₹5 لاکھ تک کا انشورنس کور فراہم کیا جا رہا ہے۔\n\nاپنا کارڈ ایکٹیویٹ کرنے کے لیے آج ہی KYC مکمل کریں۔\n\nکال کریں: +91 98102 3XXXX",
        },
        options: [
          "یہ تو کافی فائدہ مند لگ رہا ہے — میں ابھی کال کر کے اپنا آدھار شیئر کرتا ہوں۔",
          "صرف ₹20 میں ایکٹیویشن؟ ٹھیک ہے، فوراً مکمل کرتا ہوں۔",
          "میں پہلے سرکاری ویب سائٹ پر چیک کر لوں گا۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "senior-sms-1",
        type: "sms",
        title: "بجلی بورڈ وارننگ",
        content: {
          sender: "بجلی بورڈ",
          message:
            "[بجلی بورڈ]\n\nمحترم صارف، آپ کا بجلی کا کنکشن آج رات منقطع کر دیا جائے گا کیونکہ بل ادا نہیں کیا گیا اور KYC بھی نامکمل ہے۔\n\nسروس معطل ہونے سے بچنے کے لیے فوراً ویریفکیشن آفیسر سے رابطہ کریں:\n\n📞 +91 98765 4XXXX",
        },
        options: [
          "احتیاطاً میں بات کر لیتا ہوں — رات میں بجلی کٹوانے کا رسک نہیں لے سکتا۔",
          "ان کے پاس میرا میٹر نمبر ہے؟ شاید اصلی ہو، ابھی معاملہ دیکھتا ہوں۔",
          "میں بجلی بورڈ کی ایپ یا بل سے ہی تصدیق کروں گا۔ اسے نظر انداز کرتا ہوں۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "senior-whatsapp-2",
        type: "whatsapp",
        title: "حکومتی سبسڈی",
        content: {
          sender: "گورنمنٹ سبسڈی پورٹل",
          message:
            "[گورنمنٹ سبسڈی پورٹل]\n\nمحترم شہری، آپ کی ₹7,200 کی زیر التواء سبسڈی سینٹرل بینیفٹ اسکیم 2024 کے تحت ریلیز کے لیے تیار ہے۔\n\nبراہ کرم اپنا بینک اکاؤنٹ ویری فائی کریں تاکہ رقم ٹرانسفر کی جا سکے۔\n\n👉 subsidy-update-portal.in",
        },
        options: [
          "₹7,200؟ اچھی اسکیم لگ رہی ہے۔ احتیاطاً بھر دیتا ہوں۔",
          "میں صرف اکاؤنٹ نمبر ویریفائی کر دوں گا — ویسے بھی انھیں میرا نام تو معلوم ہے۔",
          "میں اسے نظر انداز کرتا ہوں۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "senior-sms-2",
        type: "sms",
        title: "PAN-آدھار لنک",
        content: {
          sender: "محکمہ انکم ٹیکس",
          message:
            "[محکمہ انکم ٹیکس]\n\nآپ کا PAN کارڈ آدھار سے منسلک نہیں ہے۔ اپڈیٹ نہ کرنے پر ₹1,000 جرمانہ اور PAN منسوخ ہو سکتا ہے۔\n\nجرمانے سے بچنے کے لیے ابھی لنک کریں:\n\n👉 pan-uid-update.in",
        },
        options: [
          "مجھے لگا لنک ہو چکا ہے، لیکن دوبارہ کر لیتا ہوں تاکہ مسئلہ نہ ہو۔",
          "₹1,000 کا جرمانہ؟ میں تفصیلات دے دیتا ہوں — بس میرا PAN بلاک نہ ہو۔",
          "میں انکم ٹیکس پورٹل پر PAN–Aadhaar کی اسٹیٹس چیک کر لوں گا۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "senior-whatsapp-3",
        type: "whatsapp",
        title: "سینئر یاترا اسکیم",
        content: {
          sender: "گورنمنٹ منظور شدہ سینئر یاترا",
          message:
            "[گورنمنٹ منظور شدہ سینئر یاترا اسکیم]\n\nصرف ₹999 میں 5 دن کی چار دھام یاترا سینئر شہریوں کے لیے۔ اس میں سفری سہولت، کھانا، اور رہائش شامل ہے۔\n\nمحدود سیٹیں دستیاب ہیں۔ ابھی Seva پورٹل کے ذریعے بک کریں:\n\n👉 senior-yatra-help.in",
        },
        options: [
          "اتنی کم قیمت؟ جلدی سے رجسٹر کر لیتا ہوں، کہیں سیٹیں ختم نہ ہو جائیں۔",
          "گورنمنٹ اپرووڈ لگ رہا ہے۔ فوراً فارم بھر دیتا ہوں۔",
          "میں پہلے آفیشل ٹورازم سائٹ پر چیک کروں گا یا اپنے بچوں سے پوچھوں گا۔",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "senior-call-1",
        type: "call",
        title: "پوتے کا حادثہ کال",
        content: {
          caller: "سٹی ہسپتال",
          message:
            "ہیلو جی، میں سٹی ہسپتال سے بات کر رہا ہوں۔ آپ کے نواسے روحان کا ایک حادثہ ہو گیا ہے۔ وہ ہوش میں ہے، لیکن ہمیں فوراً علاج شروع کرنا ہوگا۔ وقت ضائع نہیں کر سکتے — براہ کرم ₹15,000 فوری طور پر ایڈمیشن کے لیے بھیج دیں۔",
        },
        options: [
          "اوہ نہیں! براہ کرم پیمنٹ کی تفصیل بھیجیں — میں فوراً بھیجتا ہوں۔",
          "کیا وہ ٹھیک ہے؟ میں پیسے بھیج رہا ہوں، بس خیال رکھیے گا۔",
          "میں پہلے اس کے والدین اور اسپتال سے خود کال کر کے تصدیق کروں گا۔ آپ کا پورا نام اور اسپتال کا نمبر؟",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
    ],
  },
];
