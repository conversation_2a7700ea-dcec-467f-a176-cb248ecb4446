import React, { useEffect, useState, useRef } from "react";
import { Response, User } from "../types";
import { calculateRisk, getRiskProfile } from "../utils/riskCalculator";
import { DatabaseService } from "../utils/databaseService";
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Award,
  RefreshCw,
  Sparkles,
  Database,
  CheckCircle2,
  XCircle,
} from "lucide-react";
import {
  getTranslation,
  getRiskLevelTranslation,
  getRecommendationsTranslation,
} from "../utils/language";

interface ResultsProps {
  user: User;
  responses: Response[];
  onRestart: () => void;
  playClickSound: () => void;
}

const Results: React.FC<ResultsProps> = ({
  user,
  responses,
  onRestart,
  playClickSound,
}) => {
  const riskCounts = calculateRisk(responses);
  const riskProfile = getRiskProfile(riskCounts);
  const total = responses.length;

  const [saveStatus, setSaveStatus] = useState<
    "saving" | "saved" | "error" | null
  >(null);
  const saveAttemptedRef = useRef(false);

  // Translation helper
  const t = (key: string) => getTranslation(user.language, key);

  // Save results to database on component mount (only once)
  useEffect(() => {
    if (saveAttemptedRef.current) return; // Prevent multiple saves
    saveAttemptedRef.current = true;

    const saveResults = async () => {
      setSaveStatus("saving");
      try {
        const result = await DatabaseService.saveResults(user, responses);
        if (result) {
          setSaveStatus("saved");
          console.log("Results saved successfully:", result);
        } else {
          setSaveStatus("error");
          console.error("Failed to save results");
        }
      } catch (error) {
        setSaveStatus("error");
        console.error("Error saving results:", error);
      }
    };

    saveResults();
  }, [user, responses]); // Keep dependencies but use ref to prevent multiple executions

  const getRiskIcon = (level: string) => {
    switch (level) {
      case "High Risk":
        return <AlertTriangle className="w-8 h-8 text-red-400" />;
      case "Medium Risk":
        return <Shield className="w-8 h-8 text-yellow-400" />;
      default:
        return <CheckCircle className="w-8 h-8 text-green-400" />;
    }
  };

  // Get localized recommendations
  const recommendations = getRecommendationsTranslation(
    user.language,
    riskProfile.level
  );

  return (
    <div className="min-h-screen p-4 relative overflow-hidden">
      <div className="max-w-4xl mx-auto relative z-10">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-full mb-6 shadow-2xl shadow-green-500/50 border border-green-400/30">
            <Award className="w-10 h-10 text-black" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-green-400 via-green-300 to-green-200 bg-clip-text text-transparent mb-3 font-mono">
            {t("assessmentComplete")}
          </h1>
          <p className="text-green-300 text-lg font-mono">
            {t("yourRiskProfile")}, {user.name}
          </p>
          <div className="flex items-center justify-center gap-2 mt-4 text-green-400/70">
            <Sparkles className="w-4 h-4" />
            <span className="text-sm font-mono">
              {t("comprehensiveSecure")}
            </span>
            <Sparkles className="w-4 h-4" />
          </div>

          {/* Save Status Indicator */}
          <div className="flex items-center justify-center gap-2 mt-3">
            {saveStatus === "saving" && (
              <>
                <Database className="w-4 h-4 text-green-400/50 animate-pulse" />
                <span className="text-green-400/70 text-sm font-mono">
                  Saving results...
                </span>
              </>
            )}
            {saveStatus === "saved" && (
              <>
                <CheckCircle2 className="w-4 h-4 text-green-400" />
                <span className="text-green-400 text-sm font-mono">
                  Results saved
                </span>
              </>
            )}
            {saveStatus === "error" && (
              <>
                <XCircle className="w-4 h-4 text-red-400" />
                <span className="text-red-400 text-sm font-mono">
                  Save failed
                </span>
              </>
            )}
          </div>
        </div>

        {/* Risk Profile Card */}
        <div
          className={`${riskProfile.bgColor} border border-green-500/30 rounded-3xl p-8 mb-8 shadow-2xl backdrop-blur-sm`}
        >
          <div className="flex items-center justify-center mb-6">
            {getRiskIcon(riskProfile.level)}
          </div>
          <div className="text-center">
            <h2
              className={`text-3xl font-bold ${riskProfile.color} mb-4 font-mono`}
            >
              {getRiskLevelTranslation(user.language, riskProfile.level)}
            </h2>
            <p className="text-green-200 text-lg leading-relaxed font-mono">
              {riskProfile.description}
            </p>
          </div>
        </div>

        {/* Detailed Results */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          {/* Statistics */}
          <div className="bg-black/60 backdrop-blur-sm rounded-xl p-6 border border-green-500/30">
            <h3 className="text-xl font-semibold text-green-400 mb-4 flex items-center gap-2 font-mono">
              <TrendingUp className="w-5 h-5 text-green-400" />
              {t("responseBreakdown")}
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-green-300 font-mono">
                  {t("highRiskResponses")}
                </span>
                <div className="flex items-center gap-2">
                  <div className="w-12 h-2 bg-gray-800 border border-green-500/20 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-red-500 rounded-full"
                      style={{
                        width: `${(riskCounts.high_risk / total) * 100}%`,
                      }}
                    />
                  </div>
                  <span className="text-red-400 font-semibold w-8 font-mono">
                    {riskCounts.high_risk}
                  </span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-green-300 font-mono">
                  {t("mediumRiskResponses")}
                </span>
                <div className="flex items-center gap-2">
                  <div className="w-12 h-2 bg-gray-800 border border-green-500/20 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-yellow-500 rounded-full"
                      style={{
                        width: `${(riskCounts.medium_risk / total) * 100}%`,
                      }}
                    />
                  </div>
                  <span className="text-yellow-400 font-semibold w-8 font-mono">
                    {riskCounts.medium_risk}
                  </span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-green-300 font-mono">
                  {t("lowRiskResponses")}
                </span>
                <div className="flex items-center gap-2">
                  <div className="w-12 h-2 bg-gray-800 border border-green-500/20 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-green-500 rounded-full"
                      style={{
                        width: `${(riskCounts.low_risk / total) * 100}%`,
                      }}
                    />
                  </div>
                  <span className="text-green-400 font-semibold w-8 font-mono">
                    {riskCounts.low_risk}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* User Info */}
          <div className="bg-black/60 backdrop-blur-sm rounded-xl p-6 border border-green-500/30">
            <h3 className="text-xl font-semibold text-green-400 mb-4 font-mono">
              {t("assessmentDetails")}
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-green-400/70 font-mono">
                  {t("name")}:
                </span>
                <span className="text-green-300 font-mono">{user.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-400/70 font-mono">
                  {t("ageLabel")}:
                </span>
                <span className="text-green-300 font-mono">
                  {user.age} {t("years")}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-400/70 font-mono">
                  {t("language")}:
                </span>
                <span className="text-green-300 capitalize font-mono">
                  {user.language}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-400/70 font-mono">
                  {t("scenariosCompleted")}:
                </span>
                <span className="text-green-300 font-mono">{total}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-400/70 font-mono">
                  {t("assessmentDate")}:
                </span>
                <span className="text-green-300 font-mono">
                  {new Date().toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Recommendations */}
        <div className="bg-black/60 backdrop-blur-sm rounded-xl p-6 border border-green-500/30 mb-8">
          <h3 className="text-xl font-semibold text-green-400 mb-4 flex items-center gap-2 font-mono">
            <Shield className="w-5 h-5 text-green-400" />
            {t("personalizedRecommendations")}
          </h3>
          <div className="space-y-3">
            {Array.isArray(recommendations) ? (
              recommendations.map((rec, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-500/20 border border-green-500/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-green-400 text-sm font-semibold">
                      {index + 1}
                    </span>
                  </div>
                  <p className="text-green-200 font-mono">{rec}</p>
                </div>
              ))
            ) : (
              <p className="text-green-200 font-mono">
                {t("noRecommendations")}
              </p>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="text-center">
          <button
            onClick={() => {
              playClickSound();
              onRestart();
            }}
            className="inline-flex items-center gap-2 px-8 py-3 bg-gradient-to-r from-green-600 to-green-700 text-black font-semibold rounded-lg hover:from-green-500 hover:to-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-black transition-all duration-200 transform hover:scale-[1.02] shadow-xl shadow-green-500/25 font-mono"
          >
            <RefreshCw className="w-5 h-5" />
            {t("takeAssessmentAgain")}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Results;
