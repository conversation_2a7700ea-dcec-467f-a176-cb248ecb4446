import { AgeGroup } from "../types";

export const ageGroupsHindi: AgeGroup[] = [
  {
    min: 10,
    max: 17,
    scenarios: [
      {
        id: "teen-sms-1",
        type: "sms",
        title: "InstaPay सूचना",
        content: {
          sender: "InstaPay सूचना",
          message:
            'नमस्ते! "रील चैलेंज" से आपका ₹500 का कोलैब बोनस तैयार है।\n\nUPI से स्वीकार करने के लिए टैप करें: [reels-payout.in]\n\n– इंस्टाग्राम रिवॉर्ड्स ✨',
        },
        options: [
          "मैंने लिंक पर क्लिक किया और अपना UPI पिन डाल दिया। ₹500 कब आएगा?",
          "रुको, मैंने तो कोई चैलेंज में हिस्सा ही नहीं लिया। ये असली है या कोई गलती?",
          "मैं पहले इंस्टाग्राम की ऑफिशियल ऐप पर चेक करता/करती हूँ।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "teen-snapchat-1",
        type: "snapchat",
        title: "Snapchat चैट",
        content: {
          messages: [
            {
              sender: "तनिष्क",
              message:
                "अजीब लग सकता है, पर तुम Quick Add में दिखे और शायद हमारे कुछ कॉमन फ्रेंड्स हैं? 😅\n\nतुम कुछ जाने-पहचाने लग रहे थे — क्या तुम [कॉमन स्कूल का नाम] में पढ़ते हो या [लोकल जगह] के पास रहते हो?",
            },
          ],
        },
        options: [
          "हाँ, मैं वहीं पढ़ता/पढ़ती हूँ।",
          "इस मैसेज को इग्नोर कर देता/देती हूँ।",
          "तुम असली इंसान हो क्या? पहले प्रूव करो 😛 कोई सेल्फी भेजो या कुछ।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2],
        },
      },
      {
        id: "teen-instagram-post-1",
        type: "instagram_post",
        title: "Instagram पोस्ट",
        content: {
          account: "@trendie_clips_xo",
          caption:
            "नमस्ते! हम ट्रेंडी एक्सेसरीज़ (रिंग्स, क्लिप्स, चार्म्स आदि) का पुराना स्टॉक क्लियर कर रहे हैं — सब ₹99 से कम में, इस हफ्ते फ्री शिपिंग! 🎀\n\nबस हमें अपना ऑर्डर DM करें और UPI से पेमेंट करें। पहले 20 खरीदारों को एक फ्री गिफ्ट भी मिलेगा 💕\n\nहमारे टैग किए गए पोस्ट्स में रिव्यू चेक करें 💬",
          likes: "1,247",
          time: "2h",
        },
        options: [
          "ओएमजी! ये तो बहुत सस्ता है!! मुझे 3 रिंग्स और एक हेयर क्लिप चाहिए। UPI आईडी?",
          "मैं पेमेंट का स्क्रीनशॉट अभी भेज सकता/सकती हूँ, डिलीवरी अगले हफ्ते कर देना?",
          "क्या आपके पास वेबसाइट या इंस्टा चेकआउट लिंक है? और आप कहाँ से शिप कर रहे हो?",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "teen-whatsapp-1",
        type: "whatsapp",
        title: "WhatsApp संदेश",
        content: {
          sender: "EdInsights Survey",
          message:
            "🎉 नमस्ते! आपको EdInsights India की एक्सक्लूसिव स्टूडेंट रिवॉर्ड सर्वे के लिए चुना गया है।\n\nसिर्फ 2 मिनट का क्विज पूरा करें और तुरंत पाएं ₹500 के फ्री Amazon वाउचर! 🛍\n\nसीमित एंट्रीज़। अभी शुरू करें: [ed-insights-survey.in]\n\n– 5000+ छात्रों द्वारा ट्रस्टेड ✅",
        },
        options: [
          "काफी अच्छा लग रहा है! मैं अभी भरता/भरती हूँ। उम्मीद है कि असली हो 😅।",
          "क्या यह किसी ऑफिशियल साइट से है? EdInsights India का नाम कभी नहीं सुना।",
          "क्या मुझे अपना फोन नंबर देना ज़रूरी है? मैंने क्विज शुरू कर दिया है।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2],
        },
      },
      {
        id: "teen-instagram-2",
        type: "instagram",
        title: "जॉब ऑफर DM",
        content: {
          sender: "@workfromhome_students24",
          message:
            "📢 भर्ती: स्टूडेंट्स के लिए पार्ट-टाइम वर्क फ्रॉम होम जॉब\n✅ सिर्फ 1–2 घंटे रोज़\n💰 ₹5,000–₹12,000 हफ्ते में कमाएँ\n💼 कोई स्किल्स नहीं चाहिए, बस फोन और इंटरनेट\n\nअगर दिलचस्पी हो, तो DM करें 'JOB' और हम आपको पूरी डिटेल भेजेंगे। सीमित स्लॉट्स बचे हैं!\n– @workfromhome_students24",
        },
        options: [
          "JOB! काम क्या है? मुझे सच में एक साइड हसल चाहिए।",
          "क्या शुरू करने के लिए कुछ पैसे देने होंगे? मैं बाद में अरेंज कर लूंगा/लूंगी।",
          "क्या आप अपनी ऑफिशियल वेबसाइट या कंपनी का रजिस्ट्रेशन दिखा सकते हैं?",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "teen-whatsapp-2",
        type: "whatsapp",
        title: "ग्रुप चैट संदेश",
        content: {
          sender: "ग्रुप मेंबर",
          message:
            "OMG मुझे अभी इस साइट से 1700 फ्री Robux मिले!!\n\nकोई सर्वे या पासवर्ड नहीं — बस अपना यूज़रनेम डालो और जितना Robux चाहिए, चुन लो 💯\n\n100% काम करता है, मैंने पहले ही लिमिटेड्स खरीदे 🔥\n\nजल्दी ट्राय करो, इससे पहले कि ये पैच हो जाए: [robux-fast.com]\n\nबताओ तुम्हारे लिए भी काम किया क्या!!",
        },
        options: [
          "वो लिंक तो शक के लायक लग रहा है। Roblox इस तरह फ्री Robux नहीं देता।",
          "भाई अगर ये सच में काम करता है तो मैं अभी core blocks खरीदने जा रहा/रही हूँ 😂",
          "ये वही नया glitch है क्या?? मैं अभी ट्राय कर रहा/रही हूँ।",
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1],
        },
      },
    ],
  },
  {
    min: 18,
    max: 30,
    scenarios: [
      {
        id: "young-email-1",
        type: "email",
        title: "Microsoft सुरक्षा अलर्ट",
        content: {
          from: "<EMAIL>",
          subject: "⚠️ खाता सुरक्षा अलर्ट – तुरंत कार्रवाई आवश्यक",
          message:
            "प्रिय उपयोगकर्ता,\n\nहमने आपके Microsoft खाते पर मुंबई, भारत और टोरंटो, कनाडा से कई अनधिकृत लॉगिन प्रयासों का पता लगाया है।\n\nआपके खाते की सुरक्षा के लिए, हमने अस्थायी रूप से पहुंच को लॉक कर दिया है।\n\n✅ कृपया नीचे दिए गए सुरक्षित पोर्टल पर जाकर तुरंत अपनी पहचान सत्यापित करें:\n\n[अभी सत्यापित करें] → https://msverify-center.info/accountcheck\n\nयदि आप 24 घंटे के भीतर कार्रवाई नहीं करते हैं, तो आपकी सुरक्षा के लिए आपका खाता स्थायी रूप से अक्षम हो जाएगा।\n\nधन्यवाद,\nMicrosoft सुरक्षा टीम 🔒",
        },
        options: [
          "अरे नहीं, मुझे अभी इसे सत्यापित करना चाहिए। (लिंक पर क्लिक करके क्रेडेंशियल दर्ज करता है।)",
          "अरे, कौन से लॉगिन प्रयास? मैंने उन जगहों से लॉग इन नहीं किया है। (ईमेल का जवाब देता है।)",
          "मैं पहले अपना Microsoft खाता चेक करूंगा।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "young-linkedin-1",
        type: "linkedin",
        title: "LinkedIn जॉब अवसर",
        content: {
          sender: "प्रिया मेहरा, Wipro Global IT Solutions में HR सहायक",
          message:
            "नमस्ते [आपका नाम],\n\nमैं प्रिया मेहरा हूं, Wipro Global IT Solutions में HR सहायक। हमने आपकी प्रोफ़ाइल देखी है और लगता है कि आप हमारे रिमोट इंटर्नशिप प्रोग्राम के लिए बिलकुल सही हैं।\n\n✔️ लचीले घंटे\n✔️ ₹18,000/महीना स्टाइपेंड\n✔️ घर से काम\n\nबस इस छोटे से आवेदन को भरें: [wipro-careers-apply.in]\n\nसीमित स्लॉट्स हैं, इसलिए जल्दी आवेदन करें। रजिस्ट्रेशन के बाद हम ऑनबोर्डिंग स्टेप्स शेयर करेंगे। आपको टीम में पाने के लिए उत्सुक हैं!",
        },
        options: [
          "धन्यवाद, लेकिन क्या आप पुष्टि कर सकते हैं कि यह Wipro की आधिकारिक साइट या करियर पेज पर सूचीबद्ध है? मैं उसके माध्यम से आवेदन करूंगा।",
          "बहुत बढ़िया! अभी फॉर्म भरा और ID डॉक्स जमा किए। उम्मीद है कि मैं चुना जाऊंगा!",
          "क्या मुझे कोई प्रोसेसिंग फीस देनी होगी? या मुझे पहले ऑनबोर्डिंग निर्देशों का इंतजार करना चाहिए?",
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1],
        },
      },
      {
        id: "young-olx-1",
        type: "olx",
        title: "OLX खरीदार चैट",
        content: {
          buyer: "Army Personnel",
          message:
            "नमस्ते, मैंने iPhone 12 की आपकी लिस्टिंग देखी है। मुझे दिलचस्पी है और मैं तुरंत पेमेंट कर सकता हूं।\n\nमैं भारतीय सेना के साथ काम करता हूं, इसलिए व्यक्तिगत रूप से नहीं मिल सकता। मैं आपके पते पर एक पिकअप एजेंट भेजूंगा।\n\nमैं UPI के माध्यम से पहले से पेमेंट करूंगा — बस अपनी UPI ID शेयर करें। आपको जल्द ही एक पेमेंट लिंक मिलेगा, बस इसे अप्रूव करें ताकि पैसे मिल जाएं।",
        },
        options: [
          "ज़रूर, यहाँ मेरी UPI ID है। एक बार भेजने पर मुझे बताएं।",
          "ठीक है, मैंने रिक्वेस्ट अप्रूव कर दी। पिकअप कब आ रहा है?",
          "कृपया पैसे सीधे भेजें — मैं कोई अप्रूवल या कलेक्ट रिक्वेस्ट स्वीकार नहीं करता।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "young-snapchat-1",
        type: "snapchat",
        title: "Snapchat अजनबी",
        content: {
          messages: [
            {
              sender: "अजनबी",
              message:
                "अरे! पता नहीं कि हमने पहले बात की है या नहीं लेकिन आपका Bitmoji बहुत जाना-पहचाना लगा 😅\n\nआप कुछ जाने-पहचाने लग रहे हैं... क्या आप [प्रसिद्ध कॉलेज फेस्ट या कैफे] में थे? या शायद Insta या Quick Add से? हहा कोई सुराग नहीं tbh 😄",
            },
          ],
        },
        options: [
          "काफी रैंडम tbh… अगर हम कभी नहीं मिले, तो मैं आगे बात नहीं करना चाहूंगा।",
          "संदेश को इग्नोर करता हूं।",
          "हहा आप भी जाने-पहचाने लग रहे हैं। आपका Insta क्या है?",
        ],
        riskLevels: {
          high_risk: [3],
          medium_risk: [1],
          low_risk: [2],
        },
      },
      {
        id: "young-call-1",
        type: "call",
        title: "आपातकालीन कॉल",
        content: {
          caller: "Hospital Emergency",
          message:
            "हैलो?! क्या यह [आपका नाम] है?\n\nमैं [अस्पताल का नाम] से कॉल कर रहा हूं — आपका भाई अभी-अभी एक दुर्घटना में था। वह होश में है लेकिन हमें अभी आपातकालीन उपचार शुरू करना होगा।\n\nहमारे पास उसकी पूरी ID या बीमा नहीं है। क्या आप जल्दी से इस नंबर पर UPI के माध्यम से ₹10,000 भेज सकते हैं? समय महत्वपूर्ण है।",
        },
        options: [
          "रुको क्या हुआ?! ठीक है मैं अभी भेजता हूं — कृपया उसका ख्याल रखें!",
          "मेरे पास अभी 10k नहीं है, क्या मैं आधा अभी भेजूं और बाकी बाद में?",
          "यह गंभीर लगता है, लेकिन मैं पहले अपने भाई और अस्पताल को सीधे कॉल करके पुष्टि करूंगा।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "young-email-2",
        type: "email",
        title: "Accenture इंटर्नशिप",
        content: {
          from: "<EMAIL>",
          subject: "Accenture रिमोट इंटर्नशिप अवसर",
          message:
            "नमस्ते [आपका नाम],\n\nआपको Accenture के पार्टनर प्रोग्राम के तहत एक रिमोट इंटर्नशिप के लिए शॉर्टलिस्ट किया गया है।\n\n🧠 भूमिका: डिजिटल इंटर्न\n🕒 अवधि: 1 महीना\n💸 स्टाइपेंड: ₹15,000\n\nअपनी जगह पक्की करने के लिए, कृपया 24 घंटे के भीतर ऑनबोर्डिंग फॉर्म पूरा करें:\n\n👉 www.accenture-internform.live\n\nसादर,\nस्नेहा वर्मा\nHR टीम – Accenture पार्टनर नेटवर्क",
        },
        options: [
          "बहुत-बहुत धन्यवाद! मैंने फॉर्म जमा किया है और अपनी ID और रिज्यूमे शेयर किया है। ऑफर लेटर का इंतजार कर रहा हूं!",
          "धन्यवाद, लेकिन क्या आप पुष्टि कर सकते हैं कि यह इंटर्नशिप Accenture की आधिकारिक साइट पर सूचीबद्ध है? मैं सत्यापित चैनलों के माध्यम से आवेदन करना पसंद करता हूं।",
          "अरे, बस चेक कर रहा हूं — क्या स्टाइपेंड कन्फर्म है? और मुझे वेलकम किट कब मिलेगा?",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2],
        },
      },
    ],
  },
  {
    min: 31,
    max: 45,
    scenarios: [
      {
        id: "adult-olx-1",
        type: "olx",
        title: "OLX वाशिंग मशीन सेल",
        content: {
          buyer: "स्थानांतरित विक्रेता",
          conversation: [
            {
              sender: "user",
              message: "नमस्ते, क्या LG वाशिंग मशीन अभी भी उपलब्ध है?",
            },
            {
              sender: "seller",
              message:
                "जी हाँ सर, बिलकुल सही हालत में। केवल ₹8,500। हम स्थानांतरित हो रहे हैं, इसलिए जल्दी बेचना है।",
            },
            { sender: "user", message: "क्या मैं आकर देख सकता हूं?" },
            {
              sender: "seller",
              message:
                "यह पहले से ही ट्रांसपोर्ट के लिए पैक है। मैं इसे सीधे आपको डिलीवर कर सकता हूं। बस टोकन के रूप में ₹300 भेजें ताकि मैं इसे आपके लिए होल्ड कर सकूं।",
            },
          ],
        },
        options: [
          "समझ गया। मैं कन्फर्म करने के लिए अभी ₹300 भेजता हूं। धन्यवाद।",
          "ठीक है, मैं टोकन भेज दूंगा। बस सुनिश्चित करें कि डिलीवरी आज हो जाए।",
          "समझ गया, लेकिन मैं पहले से कुछ भी पेमेंट नहीं करता। अगर मैं पेमेंट से पहले देख सकूं तो बताएं।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "adult-whatsapp-1",
        type: "whatsapp",
        title: "निवेश अवसर",
        content: {
          sender: "CredGrow Capital",
          message:
            "नमस्कार सर। मैं CredGrow Capital की ओर से संपर्क कर रहा हूं, जो एक SEBI-संबद्ध फर्म है जो अल्पकालिक निश्चित रिटर्न योजनाएं प्रदान करती है।\n\n✅ ROI: केवल 15 दिनों में 25–30%\n✅ पूंजी सुरक्षित\n✅ कर-मुक्त निकासी\n\nIT और वित्त के कई पेशेवर पहले से ही जुड़ चुके हैं। हम इस सप्ताह 10 और ग्राहकों को ऑनबोर्ड कर रहे हैं।\n\nमैं ब्रोशर और आज के लाइव पेआउट स्क्रीनशॉट शेयर कर सकता हूं। अगर आप एक त्वरित कॉल के लिए तैयार हैं तो बताएं।",
        },
        options: [
          "ठोस लगता है। क्या आप मुझे योजना की विवरण और पेमेंट स्टेप्स भेज सकते हैं?",
          "मुझे पहले आपकी फर्म के SEBI रजिस्ट्रेशन और वेबसाइट को वेरिफाई करना होगा — क्या आप वह शेयर कर सकते हैं?",
          "अच्छा लग रहा है। क्या मैं ₹5,000 से शुरू करके बाद में बढ़ा सकता हूं?",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2],
        },
      },
      {
        id: "adult-sms-1",
        type: "sms",
        title: "FASTag सूचना",
        content: {
          sender: "NHAI FASTag",
          message:
            "[FASTag सूचना]\n\nआपकी FASTag सेवाएं 27 जून को अनपेड टोल उल्लंघन के कारण अस्थायी रूप से निलंबित की गई हैं।\n\n₹500 जुर्माने से बचने के लिए ₹87.50 की बकाया राशि का भुगतान करें।\n\nअभी सक्रिय करें: fastag-clearance.in/verify\n\n– NHAI FASTag सपोर्ट",
        },
        options: [
          "अरे मुझे नहीं पता था कि टोल की समस्या थी। मैं अभी इसे पे करके क्लियर करवाता हूं।",
          "लिंक पर क्लिक करके UPI पिन डालता हूं।",
          "मैं NHAI की आधिकारिक साइट या ऐप पर सीधे अपना FASTag अकाउंट चेक करूंगा।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "adult-call-1",
        type: "call",
        title: "HDFC रिवॉर्ड्स कॉल",
        content: {
          caller: "HDFC Bank रिवॉर्ड्स",
          message:
            "नमस्कार, मैं HDFC Bank के रिवॉर्ड पॉइंट्स विभाग से कॉल कर रहा हूं।\n\nआपके क्रेडिट कार्ड पॉइंट्स आज एक्सपायर होने वाले हैं — इनकी वैल्यू ₹3,700 है।\n\nमैं अभी इन्हें रिडीम करने में आपकी मदद कर सकता हूं और इन्हें कैशबैक या Amazon वाउचर में कन्वर्ट कर सकता हूं। प्रक्रिया के लिए बस आपका कार्ड नंबर और एक्सपायरी डेट सत्यापित करना होगा।",
        },
        options: [
          "मैं इसे वेरिफाई करने के लिए अपने कार्ड पर दिए HDFC नंबर पर कॉल करूंगा। धन्यवाद।",
          "ठीक है, मेरा कार्ड नंबर 1890 पर खत्म होता है। क्या आप रिडेम्पशन प्रोसेस कर सकते हैं?",
          "रुकिए, क्या मैं आज रिडीम नहीं करूंगा तो पॉइंट्स खो जाएंगे?",
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1],
        },
      },
      {
        id: "adult-sms-2",
        type: "sms",
        title: "Delhivery पैकेज",
        content: {
          sender: "Delhivery",
          message:
            "[Delhivery सूचना]\n\nअधूरे पते की जानकारी के कारण आपका पैकेज डिलीवर नहीं हो सका।\n\nवापसी से बचने के लिए कृपया 24 घंटे के भीतर अपना पता अपडेट करें:\n\n👉 www.delhivery-update.info",
        },
        options: [
          "लिंक पर क्लिक किया और अपना पता डाला। इसमें ₹45 मांगे तो समस्या से बचने के लिए पे कर दिया।",
          "क्या यह मेरा delhivery ऑर्डर है? मैं सेफ रहने के लिए बस डिटेल्स कम्पलीट कर देता हूं।",
          "मैं कूरियर की असली वेबसाइट के माध्यम से अपना पैकेज ट्रैक करूंगा।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "adult-call-2",
        type: "call",
        title: "अस्पताल आपातकाल",
        content: {
          caller: "जीवन रक्षा अस्पताल",
          message:
            "हैलो?! क्या यह [आपका नाम] है?\n\nमैं जीवन रक्षा अस्पताल से कॉल कर रहा हूं। एक दुर्घटना हुई है — आपका छोटा भाई अभी-अभी बाइक क्रैश के बाद लाया गया है। वह होश में है लेकिन खून बह रहा है। हमें तुरंत इलाज शुरू करना होगा।\n\nहमें उसका बटुआ या बीमा कार्ड नहीं मिला। यदि आप आपातकालीन एडमिशन फीस में मदद कर सकें — ₹8,500, तो हम प्रक्रिया शुरू कर सकते हैं। UPI या GPay ठीक है। और कृपया जल्दी आइए।",
        },
        options: [
          "मैं समझता हूं, लेकिन मैं पहले अपने भाई और अस्पताल को सीधे कॉल करूंगा। मैं आपको वापस कॉल करूंगा।",
          "जी हाँ, कृपया तुरंत उसका इलाज शुरू करें। मैं अभी पैसे भेज रहा हूं!",
          "क्या वह बुरी तरह घायल है? क्या मैं आधा अभी भेजूं और बाकी बाद में?",
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1],
        },
      },
    ],
  },
  {
    min: 46,
    max: 60,
    scenarios: [
      {
        id: "middle-call-1",
        type: "call",
        title: "Microsoft सपोर्ट कॉल",
        content: {
          caller: "Microsoft सपोर्ट",
          message:
            "नमस्ते मैडम, मैं Microsoft सपोर्ट से राकेश हूं।\n\nहमने आपके कंप्यूटर पर विदेशी स्थानों से संदिग्ध लॉगिन प्रयासों का पता लगाया है। आपकी बैंकिंग और ईमेल जोखिम में हो सकती है।\n\nमैं आपके सिस्टम को सुरक्षित करने में मदद करूंगा — कृपया हमारा सपोर्ट टूल इंस्टॉल करें ताकि मैं रिमोटली सहायता कर सकूं।",
        },
        options: [
          "ठीक है, मैंने इसे इंस्टॉल कर लिया है। अब मैं क्या करूं?",
          "ठीक है, लेकिन एक्सेस देने से पहले, क्या आप मुझे कोई ID या कंपनी ईमेल दिखा सकते हैं?",
          "माफ करें, मैं रिमोट एक्सेस की अनुमति नहीं देती। मैं अपने स्थानीय तकनीशियन से चेक कराऊंगी।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "middle-sms-1",
        type: "sms",
        title: "Netflix अलर्ट",
        content: {
          sender: "Netflix",
          message:
            "[Netflix अलर्ट]\n\nऑटो-रिन्यूअल फेल होने के कारण आपकी सब्सक्रिप्शन निलंबित कर दी गई है।\n\nसेवा में बाधा से बचने के लिए कृपया अपनी पेमेंट जानकारी सत्यापित करें:\n\n👉 netflix-support-help.in",
        },
        options: [
          "मैं यहीं अपना कार्ड अपडेट कर देती हूं।",
          "मैं Netflix ऐप में चेक करूंगी।",
          "शायद एक्सपायर हो गया हो — मैं लिंक चेक करती हूं।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [3],
          low_risk: [2],
        },
      },
      {
        id: "middle-whatsapp-1",
        type: "whatsapp",
        title: "Flipkart इनाम",
        content: {
          sender: "Flipkart रिवॉर्ड्स",
          message:
            "🎊 बधाई हो! 8742 पर समाप्त होने वाले आपके नंबर ने हमारी पिछली त्योहार मेगा सेल से ₹50,000 का Flipkart स्क्रैच कार्ड जीता है।\n\nअपने विवरण सत्यापित करके अभी दावा करें:\n\n👉 flipkart-prize.in",
        },
        options: [
          "वाह! मैंने पिछले त्योहार में खरीदारी की थी। मैं इसे चेक करती हूं।",
          "इसमें Flipkart लिखा है — मैं फॉर्म जल्दी भर देती हूं।",
          "यह फेक लगता है। मैं Flipkart के ऐप या सपोर्ट पेज पर सीधे चेक करूंगी।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "middle-sms-2",
        type: "sms",
        title: "आवास सर्वेक्षण",
        content: {
          sender: "जिला आवास",
          message:
            "[जिला आवास सर्वेक्षण]\n\n2024 शहरी संपत्ति सत्यापन अभियान के हिस्से के रूप में, सभी घर मालिकों को अपने रिकॉर्ड अपडेट करने के लिए एक छोटा सर्वेक्षण पूरा करना होगा।\n\nजवाब न देने पर अधूरे दस्तावेजीकरण के लिए आपका नाम फ्लैग किया जा सकता है।\n\nअभी फॉर्म पूरा करें:\n\n👉 district-housing-update.in",
        },
        options: [
          "मैं इसे आधिकारिक सरकारी साइट पर चेक करूंगी या अपने स्थानीय कार्यालय को कॉल करूंगी।",
          "आधिकारिक लगता है। मैं डेडलाइन से पहले इसे जल्दी भर देती हूं।",
          "पता नहीं कि यह असली है या नहीं, लेकिन सुरक्षा के लिए कर देती हूं।",
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [3],
          low_risk: [1],
        },
      },
      {
        id: "middle-olx-1",
        type: "olx",
        title: "आर्मी अफसर खरीदार",
        content: {
          buyer: "राज (आर्मी)",
          conversation: [
            {
              sender: "raj",
              message:
                "नमस्ते सर, मुझे आपकी लिस्ट की गई LG वाशिंग मशीन में दिलचस्पी है। क्या यह अभी भी उपलब्ध है?",
            },
            { sender: "user", message: "जी हाँ, अभी भी उपलब्ध है।" },
            {
              sender: "raj",
              message:
                "बहुत बढ़िया, मैं इसे ले लूंगा। मैं आर्मी में हूं, एक प्रतिबंधित क्षेत्र में तैनात हूं, इसलिए व्यक्तिगत रूप से नहीं आ सकता।\n\nमैं इसे उठाने के लिए एक पिकअप एजेंट भेजूंगा, और उससे पहले पूरी राशि ट्रांसफर कर दूंगा। बस अपनी UPI ID भेजें। आपको एक पेमेंट कन्फर्मेशन रिक्वेस्ट मिलेगी — कृपया इसे अप्रूव करें ताकि पेमेंट हो जाए।",
            },
          ],
        },
        options: [
          "ज़रूर, UPI है mohit@okaxis। भेजने के बाद बताएं।",
          "ठीक है, मैंने रिक्वेस्ट अप्रूव कर दी। पिकअप कब आ रहा है?",
          "कृपया पैसे सीधे भेजें — मैं कोई अप्रूवल या कलेक्ट रिक्वेस्ट स्वीकार नहीं करता।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "middle-sms-3",
        type: "sms",
        title: "SBI रिवॉर्ड्स",
        content: {
          sender: "SBI Card",
          message:
            "[SBI रिवॉर्ड्स]\n\nप्रिय ग्राहक, आपके क्रेडिट कार्ड के ₹3,250 के पॉइंट्स आज एक्सपायर हो रहे हैं।\n\nकैशबैक या गिफ्ट वाउचर के लिए अभी रिडीम करें।\n\nदावा करने के लिए अभी कॉल करें: +91 98765 4XXXX",
        },
        options: [
          "जब वे पूछेंगे तो मैं अपना कार्ड नंबर कन्फर्म कर दूंगी। जल्दी हो जाना चाहिए।",
          "ठीक है, अभी कॉल करती हूं — मैं पॉइंट्स नहीं खोना चाहती।",
          "बेहतर है कि SBI Card ऐप पर चेक करूं। मैं अज्ञात नंबरों पर भरोसा नहीं करूंगी।",
        ],
        riskLevels: {
          high_risk: [2],
          medium_risk: [1],
          low_risk: [3],
        },
      },
    ],
  },
  {
    min: 61,
    max: 90,
    scenarios: [
      {
        id: "senior-whatsapp-1",
        type: "whatsapp",
        title: "आयुष्मान भारत कार्ड",
        content: {
          sender: "आयुष्मान भारत",
          message:
            "[आयुष्मान भारत सीनियर कार्ड]\n\nभारत सरकार 60 वर्ष से ऊपर के सभी वरिष्ठ नागरिकों के लिए मुफ्त स्वास्थ्य जांच और ₹5 लाख कवरेज की पेशकश कर रही है।\n\nअपना कार्ड सक्रिय करने के लिए, आज KYC पूरा करें।\n\nअभी कॉल करें: +91 98102 3XXXX",
        },
        options: [
          "यह उपयोगी लगता है — मैं अभी उन्हें कॉल करके अपना आधार शेयर करूंगा।",
          "एक्टिवेशन के लिए केवल ₹20? ठीक है, मैं जल्दी करवा लेता हूं।",
          "मैं पहले इसे आधिकारिक सरकारी वेबसाइट पर चेक करूंगा।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "senior-sms-1",
        type: "sms",
        title: "बिजली बोर्ड चेतावनी",
        content: {
          sender: "बिजली बोर्ड",
          message:
            "[बिजली बोर्ड]\n\nप्रिय ग्राहक, अनपेड बिल और KYC फेलियर के कारण आपका पावर कनेक्शन आज रात काट दिया जाएगा।\n\nसस्पेंशन से बचने के लिए, तुरंत सत्यापन अधिकारी को कॉल करें:\n\n📞 +91 98765 4XXXX",
        },
        options: [
          "सुरक्षित रहने के लिए, मैं उनसे बात करूंगा — मैं रात में कटौती का जोखिम नहीं ले सकता।",
          "उनके पास मेरा मीटर नंबर है? यह वैध होना चाहिए। मैं अभी इसे सुलझाता हूं।",
          "मैं इसे बिजली बोर्ड ऐप या बिल के माध्यम से प्राप्त करूंगा। मैं इसे इग्नोर करूंगा।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "senior-whatsapp-2",
        type: "whatsapp",
        title: "सरकारी सब्सिडी",
        content: {
          sender: "सरकारी सब्सिडी पोर्टल",
          message:
            "[सरकारी सब्सिडी पोर्टल]\n\nप्रिय नागरिक, केंद्रीय लाभ योजना 2024 के तहत आपकी ₹7,200 की लंबित सब्सिडी रिलीज के लिए तैयार है।\n\nक्रेडिट प्राप्त करने के लिए कृपया अपनी बैंक विवरण सत्यापित करें।\n\n👉 subsidy-update-portal.in",
        },
        options: [
          "₹7,200? अच्छी योजना। मैं इसे सुरक्षा के लिए भर देता हूं।",
          "मैं अकाउंट नंबर वेरिफाई कर दूंगा — उनके पास मेरा नाम तो पहले से है।",
          "मैं इसे इग्नोर करूंगा।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "senior-sms-2",
        type: "sms",
        title: "PAN-आधार लिंक",
        content: {
          sender: "आयकर विभाग",
          message:
            "[आयकर विभाग]\n\nआपका PAN आधार से लिंक नहीं है। अपडेट करने में विफलता के परिणामस्वरूप ₹1,000 जुर्माना और PAN निष्क्रियकरण होगा।\n\nजुर्माने से बचने के लिए अभी लिंक करें:\n\n👉 pan-uid-update.in",
        },
        options: [
          "मुझे लगता था कि मैंने इसे लिंक कर दिया था, लेकिन सुरक्षित रहने के लिए मैं इसे दोबारा करूंगा।",
          "₹1,000 जुर्माना? मैं विवरण शेयर करूंगा — बस सुनिश्चित करें कि मेरा PAN ब्लॉक न हो।",
          "मैं आयकर पोर्टल पर अपना PAN-आधार स्टेटस एक बार चेक करूंगा।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "senior-whatsapp-3",
        type: "whatsapp",
        title: "सीनियर यात्रा योजना",
        content: {
          sender: "सरकारी सीनियर यात्रा",
          message:
            "[सरकार अनुमोदित सीनियर यात्रा योजना]\n\nवरिष्ठ नागरिकों के लिए केवल ₹999 में 5-दिवसीय चार धाम यात्रा प्राप्त करें, जिसमें यात्रा, भोजन और आवास शामिल है।\n\nसीमित स्लॉट उपलब्ध। सेवा पोर्टल के माध्यम से अभी बुक करें:\n\n👉 senior-yatra-help.in",
        },
        options: [
          "इतनी कम कीमत! सभी स्लॉट खत्म होने से पहले मैं रजिस्टर करता हूं।",
          "सरकार अनुमोदित लगता है। मैं फॉर्म जल्दी भर देता हूं।",
          "मैं आधिकारिक पर्यटन साइट से चेक करूंगा या पहले अपने बच्चों से पूछूंगा।",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
      {
        id: "senior-call-1",
        type: "call",
        title: "पोता दुर्घटना कॉल",
        content: {
          caller: "सिटी अस्पताल",
          message:
            "नमस्ते जी, मैं सिटी अस्पताल से कॉल कर रहा हूं। आपका पोता रोहन एक दुर्घटना में है। वह होश में है, लेकिन हमें तुरंत इलाज शुरू करना होगा। समय नहीं है — कृपया एडमिशन की औपचारिकताओं के लिए तुरंत ₹15,000 भेजें।",
        },
        options: [
          "अरे नहीं! कृपया पेमेंट डिटेल्स भेजें — मैं तुरंत ट्रांसफर करूंगा।",
          "क्या वह ठीक है? मैं पैसे भेज दूंगा, बस कृपया उसका ख्याल रखें।",
          "मैं पहले उसके माता-पिता को कॉल करके कन्फर्म करूंगा। आपका पूरा नाम और अस्पताल नंबर क्या है?",
        ],
        riskLevels: {
          high_risk: [1],
          medium_risk: [2],
          low_risk: [3],
        },
      },
    ],
  },
];
