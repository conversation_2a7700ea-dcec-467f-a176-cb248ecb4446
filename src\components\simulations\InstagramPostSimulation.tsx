import React from 'react';
import { <PERSON><PERSON><PERSON> } from '../../types';
import { Heart, MessageCircle, Send, Bookmark, MoreHorizontal } from 'lucide-react';

interface InstagramPostSimulationProps {
  scenario: Scenario;
  onResponse: (optionIndex: number) => void;
}

const InstagramPostSimulation: React.FC<InstagramPostSimulationProps> = ({ scenario, onResponse }) => {
  const content = scenario.content as { account: string; caption: string; likes: string; time: string };

  return (
    <div className="max-w-md mx-auto">
      {/* Phone Frame */}
      <div className="bg-slate-900 rounded-3xl p-2 shadow-2xl">
        {/* Instagram Post Interface */}
        <div className="bg-black rounded-2xl overflow-hidden">
          {/* Header */}
          <div className="bg-black px-4 py-3 border-b border-slate-800">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-tr from-purple-400 via-pink-400 to-orange-400 rounded-full p-[2px]">
                  <div className="w-full h-full bg-black rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold text-xs">
                      {content.account.replace('@', '').charAt(0).toUpperCase()}
                    </span>
                  </div>
                </div>
                <div>
                  <h3 className="text-white font-medium text-sm">{content.account}</h3>
                  <p className="text-slate-400 text-xs">{content.time}</p>
                </div>
              </div>
              <MoreHorizontal className="w-5 h-5 text-white" />
            </div>
          </div>

          {/* Post Image Placeholder */}
          <div className="bg-gradient-to-br from-pink-500 via-purple-500 to-blue-500 h-64 flex items-center justify-center">
            <div className="text-center text-white">
              <div className="text-4xl mb-2">🎀</div>
              <p className="text-sm font-medium">Trendy Accessories</p>
              <p className="text-xs opacity-80">Under ₹99 Collection</p>
            </div>
          </div>

          {/* Post Actions */}
          <div className="px-4 py-3">
            <div className="flex items-center justify-between mb-3">
              <div className="flex gap-4">
                <Heart className="w-6 h-6 text-white" />
                <MessageCircle className="w-6 h-6 text-white" />
                <Send className="w-6 h-6 text-white" />
              </div>
              <Bookmark className="w-6 h-6 text-white" />
            </div>
            
            <p className="text-white text-sm font-medium mb-2">{content.likes} likes</p>
            
            {/* Caption */}
            <div className="text-white text-sm">
              <span className="font-medium">{content.account}</span>
              <span className="ml-2">{content.caption}</span>
            </div>

            {/* Comments Preview */}
            <div className="mt-3 space-y-1">
              <p className="text-slate-400 text-xs">View all 47 comments</p>
              <div className="text-white text-xs">
                <span className="font-medium">@fashionista_riya</span>
                <span className="ml-1 text-slate-300">Omg so cute! 😍</span>
              </div>
              <div className="text-white text-xs">
                <span className="font-medium">@style_queen_24</span>
                <span className="ml-1 text-slate-300">Just ordered 3! 💕</span>
              </div>
            </div>

            {/* Add Comment */}
            <div className="flex items-center gap-3 mt-3 pt-2 border-t border-slate-800">
              <div className="w-6 h-6 bg-slate-600 rounded-full"></div>
              <span className="text-slate-500 text-sm flex-1">Add a comment...</span>
            </div>
          </div>
        </div>
      </div>

      {/* Response Options */}
      <div className="mt-8 space-y-3">
        <h4 className="text-white font-medium mb-4">How would you respond?</h4>
        {scenario.options.map((option, index) => (
          <button
            key={index}
            onClick={() => onResponse(index + 1)}
            className="w-full p-4 text-left bg-slate-800/50 border border-slate-700 rounded-lg text-slate-200 hover:bg-slate-700/50 hover:border-slate-600 transition-all duration-200"
          >
            <span className="text-pink-400 font-medium">{index + 1}. </span>
            {option}
          </button>
        ))}
      </div>
    </div>
  );
};

export default InstagramPostSimulation;