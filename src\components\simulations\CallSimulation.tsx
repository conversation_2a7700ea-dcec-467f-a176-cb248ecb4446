import React from 'react';
import { <PERSON><PERSON><PERSON> } from '../../types';
import { Phone, PhoneCall, Mic, MicOff, Volume2 } from 'lucide-react';

interface CallSimulationProps {
  scenario: Scenario;
  onResponse: (optionIndex: number) => void;
}

const CallSimulation: React.FC<CallSimulationProps> = ({ scenario, onResponse }) => {
  const content = scenario.content as { caller: string; message: string };

  return (
    <div className="max-w-md mx-auto">
      {/* Phone Frame */}
      <div className="bg-slate-900 rounded-3xl p-2 shadow-2xl">
        {/* Call Interface */}
        <div className="bg-gradient-to-b from-green-600 to-green-700 rounded-2xl overflow-hidden">
          {/* Call Status */}
          <div className="text-center py-8 px-6">
            <div className="w-24 h-24 bg-white/20 rounded-full mx-auto mb-4 flex items-center justify-center">
              <Phone className="w-12 h-12 text-white" />
            </div>
            <h3 className="text-white text-xl font-semibold mb-2">{content.caller}</h3>
            <p className="text-green-100 text-sm mb-1">Incoming call</p>
            <p className="text-green-200 text-xs">00:45</p>
          </div>

          {/* Call Content/Speech */}
          <div className="bg-black/20 mx-4 mb-6 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <PhoneCall className="w-5 h-5 text-white/80 mt-1 flex-shrink-0" />
              <p className="text-white text-sm leading-relaxed">
                "{content.message}"
              </p>
            </div>
          </div>

          {/* Call Controls */}
          <div className="flex justify-center gap-6 pb-8">
            <button className="w-14 h-14 bg-white/20 rounded-full flex items-center justify-center">
              <Mic className="w-6 h-6 text-white" />
            </button>
            <button className="w-14 h-14 bg-white/20 rounded-full flex items-center justify-center">
              <Volume2 className="w-6 h-6 text-white" />
            </button>
            <button className="w-14 h-14 bg-red-500 rounded-full flex items-center justify-center">
              <Phone className="w-6 h-6 text-white transform rotate-135" />
            </button>
          </div>
        </div>
      </div>

      {/* Response Options */}
      <div className="mt-8 space-y-3">
        <h4 className="text-white font-medium mb-4">How would you respond?</h4>
        {scenario.options.map((option, index) => (
          <button
            key={index}
            onClick={() => onResponse(index + 1)}
            className="w-full p-4 text-left bg-slate-800/50 border border-slate-700 rounded-lg text-slate-200 hover:bg-slate-700/50 hover:border-slate-600 transition-all duration-200"
          >
            <span className="text-green-400 font-medium">{index + 1}. </span>
            {option}
          </button>
        ))}
      </div>
    </div>
  );
};

export default CallSimulation;