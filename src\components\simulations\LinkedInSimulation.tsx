import React from 'react';
import { <PERSON><PERSON><PERSON> } from '../../types';
import { Briefcase, MessageSquare, ThumbsUp, Share } from 'lucide-react';

interface LinkedInSimulationProps {
  scenario: Scenario;
  onResponse: (optionIndex: number) => void;
}

const LinkedInSimulation: React.FC<LinkedInSimulationProps> = ({ scenario, onResponse }) => {
  const content = scenario.content as { sender: string; message: string };

  return (
    <div className="max-w-lg mx-auto">
      {/* LinkedIn Message Interface */}
      <div className="bg-white rounded-lg overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="bg-blue-700 px-4 py-3 flex items-center gap-3">
          <Briefcase className="w-6 h-6 text-white" />
          <h3 className="text-white font-semibold">LinkedIn</h3>
        </div>

        {/* Message Header */}
        <div className="bg-blue-50 px-4 py-3 border-b">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold">
                {content.sender.split(' ')[0].charAt(0)}
              </span>
            </div>
            <div>
              <h4 className="font-semibold text-slate-800">{content.sender}</h4>
              <p className="text-blue-600 text-sm">• 1st</p>
            </div>
          </div>
        </div>

        {/* Message Content */}
        <div className="p-4">
          <div className="bg-slate-50 rounded-lg p-4">
            <p className="text-slate-800 leading-relaxed whitespace-pre-line">
              {content.message}
            </p>
            <div className="flex items-center gap-4 mt-4 pt-3 border-t border-slate-200">
              <span className="text-slate-500 text-sm">2 hours ago</span>
              <div className="flex gap-3 ml-auto">
                <ThumbsUp className="w-4 h-4 text-slate-400 hover:text-blue-600 cursor-pointer" />
                <MessageSquare className="w-4 h-4 text-slate-400 hover:text-blue-600 cursor-pointer" />
                <Share className="w-4 h-4 text-slate-400 hover:text-blue-600 cursor-pointer" />
              </div>
            </div>
          </div>
        </div>

        {/* Reply Area */}
        <div className="bg-slate-50 px-4 py-3 border-t">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-slate-300 rounded-full flex items-center justify-center">
              <span className="text-slate-600 text-sm font-semibold">You</span>
            </div>
            <div className="flex-1 bg-white rounded-full px-4 py-2 border">
              <span className="text-slate-500 text-sm">Write a message...</span>
            </div>
          </div>
        </div>
      </div>

      {/* Response Options */}
      <div className="mt-8 space-y-3">
        <h4 className="text-white font-medium mb-4">How would you respond?</h4>
        {scenario.options.map((option, index) => (
          <button
            key={index}
            onClick={() => onResponse(index + 1)}
            className="w-full p-4 text-left bg-slate-800/50 border border-slate-700 rounded-lg text-slate-200 hover:bg-slate-700/50 hover:border-slate-600 transition-all duration-200"
          >
            <span className="text-blue-400 font-medium">{index + 1}. </span>
            {option}
          </button>
        ))}
      </div>
    </div>
  );
};

export default LinkedInSimulation;