import React from 'react';
import { <PERSON><PERSON><PERSON> } from '../../types';
import { Heart, MessageCircle, Send, Bookmark, MoreHorizontal } from 'lucide-react';

interface InstagramSimulationProps {
  scenario: Scenario;
  onResponse: (optionIndex: number) => void;
}

const InstagramSimulation: React.FC<InstagramSimulationProps> = ({ scenario, onResponse }) => {
  const content = scenario.content as { sender: string; message: string };

  return (
    <div className="max-w-md mx-auto">
      {/* Phone Frame */}
      <div className="bg-slate-900 rounded-3xl p-2 shadow-2xl">
        {/* Instagram DM Interface */}
        <div className="bg-black rounded-2xl overflow-hidden">
          {/* Header */}
          <div className="bg-slate-900 px-4 py-3 border-b border-slate-800">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-tr from-purple-400 via-pink-400 to-orange-400 rounded-full p-[2px]">
                <div className="w-full h-full bg-black rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold text-xs">
                    {content.sender.replace('@', '').charAt(0).toUpperCase()}
                  </span>
                </div>
              </div>
              <div>
                <h3 className="text-white font-medium text-sm">{content.sender}</h3>
                <p className="text-slate-400 text-xs">Active now</p>
              </div>
            </div>
          </div>

          {/* Message Area */}
          <div className="p-4 min-h-[250px]">
            <div className="flex justify-start">
              <div className="bg-slate-800 rounded-2xl rounded-tl-sm p-3 max-w-[85%]">
                <p className="text-white text-sm leading-relaxed whitespace-pre-line">
                  {content.message}
                </p>
                <p className="text-slate-400 text-xs mt-2">2m</p>
              </div>
            </div>
          </div>

          {/* Input Area */}
          <div className="px-4 py-3 border-t border-slate-800">
            <div className="flex items-center gap-3">
              <div className="flex-1 bg-slate-800 rounded-full px-4 py-2">
                <span className="text-slate-500 text-sm">Message...</span>
              </div>
              <div className="flex gap-2">
                <Heart className="w-6 h-6 text-slate-400" />
                <Send className="w-6 h-6 text-slate-400" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Response Options */}
      <div className="mt-8 space-y-3">
        <h4 className="text-white font-medium mb-4">How would you respond?</h4>
        {scenario.options.map((option, index) => (
          <button
            key={index}
            onClick={() => onResponse(index + 1)}
            className="w-full p-4 text-left bg-slate-800/50 border border-slate-700 rounded-lg text-slate-200 hover:bg-slate-700/50 hover:border-slate-600 transition-all duration-200"
          >
            <span className="text-pink-400 font-medium">{index + 1}. </span>
            {option}
          </button>
        ))}
      </div>
    </div>
  );
};

export default InstagramSimulation;