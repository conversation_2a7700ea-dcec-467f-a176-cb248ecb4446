import React from 'react';
import { <PERSON><PERSON><PERSON> } from '../../types';
import { MessageCircle, Phone, Video, MoreVertical } from 'lucide-react';

interface WhatsAppSimulationProps {
  scenario: Scenario;
  onResponse: (optionIndex: number) => void;
}

const WhatsAppSimulation: React.FC<WhatsAppSimulationProps> = ({ scenario, onResponse }) => {
  const content = scenario.content as { sender: string; message: string };

  return (
    <div className="max-w-md mx-auto">
      {/* Phone Frame */}
      <div className="bg-slate-900 rounded-3xl p-2 shadow-2xl">
        {/* WhatsApp Interface */}
        <div className="bg-green-600 rounded-2xl overflow-hidden">
          {/* Header */}
          <div className="bg-green-700 px-4 py-3 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-slate-300 rounded-full flex items-center justify-center">
                <span className="text-slate-700 font-semibold text-sm">
                  {content.sender.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <h3 className="text-white font-medium">{content.sender}</h3>
                <p className="text-green-200 text-xs">online</p>
              </div>
            </div>
            <div className="flex gap-4">
              <Phone className="w-5 h-5 text-white" />
              <Video className="w-5 h-5 text-white" />
              <MoreVertical className="w-5 h-5 text-white" />
            </div>
          </div>

          {/* Chat Area */}
          <div className="bg-green-50 p-4 min-h-[250px] relative" 
               style={{backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e5f7e5' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-10V0h-2v24h2z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`}}>
            <div className="flex justify-start">
              <div className="bg-white rounded-lg rounded-tl-sm p-3 max-w-[80%] shadow-sm">
                <p className="text-slate-800 text-sm leading-relaxed whitespace-pre-line">
                  {content.message}
                </p>
                <p className="text-slate-500 text-xs mt-1 text-right">12:34 PM</p>
              </div>
            </div>
          </div>

          {/* Input Area */}
          <div className="bg-green-100 px-4 py-2">
            <div className="bg-white rounded-full px-4 py-2 flex items-center">
              <MessageCircle className="w-5 h-5 text-slate-500" />
              <span className="ml-2 text-slate-500 text-sm">Type a message...</span>
            </div>
          </div>
        </div>
      </div>

      {/* Response Options */}
      <div className="mt-8 space-y-3">
        <h4 className="text-white font-medium mb-4">How would you respond?</h4>
        {scenario.options.map((option, index) => (
          <button
            key={index}
            onClick={() => onResponse(index + 1)}
            className="w-full p-4 text-left bg-slate-800/50 border border-slate-700 rounded-lg text-slate-200 hover:bg-slate-700/50 hover:border-slate-600 transition-all duration-200"
          >
            <span className="text-green-400 font-medium">{index + 1}. </span>
            {option}
          </button>
        ))}
      </div>
    </div>
  );
};

export default WhatsAppSimulation;