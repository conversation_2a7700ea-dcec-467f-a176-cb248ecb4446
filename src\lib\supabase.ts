import { createClient } from "@supabase/supabase-js";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error("Missing Supabase environment variables");
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types for our database
export interface ScamRiskResult {
  id?: number;
  name: string;
  age: number;
  preferred_language: string;
  high_risk: number;
  medium_risk: number;
  low_risk: number;
  overall_risk: string;
  created_at?: string;
}

// Types for teen responses table (13-17 age group)
export interface TeenResponse {
  id?: number;
  response_id: number; // Foreign key to scam_risk_results
  name: string;
  age: number;
  preferred_language: string;
  high_risk: number;
  medium_risk: number;
  low_risk: number;
  overall_risk: string;
  scenario_1_risk_issues?: string[]; // SMS scenario
  scenario_2_risk_issues?: string[]; // WhatsApp scenario
  scenario_3_risk_issues?: string[]; // Instagram scenario
  scenario_4_risk_issues?: string[]; // Email scenario
  scenario_5_risk_issues?: string[]; // Call scenario
  scenario_6_risk_issues?: string[]; // OLX scenario
  created_at?: string;
}

// Combined view type for teen responses with main assessment data
export interface TeenResponseWithMain extends TeenResponse {
  // This interface represents the teen_responses_with_main view
  main_response_created_at?: string;
}
