import React, { useEffect } from "react";
import { <PERSON><PERSON><PERSON> } from "../types";
import SMSSimulation from "./simulations/SMSSimulation";
import WhatsAppSimulation from "./simulations/WhatsAppSimulation";
import InstagramSimulation from "./simulations/InstagramSimulation";
import InstagramPostSimulation from "./simulations/InstagramPostSimulation";
import EmailSimulation from "./simulations/EmailSimulation";
import CallSimulation from "./simulations/CallSimulation";
import OLXSimulation from "./simulations/OLXSimulation";
import LinkedInSimulation from "./simulations/LinkedInSimulation";
import SnapchatSimulation from "./simulations/SnapchatSimulation";
import { ChevronRight, Shield } from "lucide-react";
import { getTranslation } from "../utils/language";

interface SurveyProps {
  scenario: Scenario;
  currentQuestion: number;
  totalQuestions: number;
  onResponse: (optionIndex: number) => void;
  userLanguage: "english" | "hindi" | "urdu" | "telugu";
  playClickSound: () => void;
}

const Survey: React.FC<SurveyProps> = ({
  scenario,
  currentQuestion,
  totalQuestions,
  onResponse,
  userLanguage,
  playClickSound,
}) => {
  const t = (key: string) => getTranslation(userLanguage, key);

  // Wrapper function to add click sound to responses
  const handleResponseWithSound = (optionIndex: number) => {
    playClickSound();
    onResponse(optionIndex);
  };

  // Scroll to top when scenario changes (after user selects a response)
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [currentQuestion]); // Trigger when currentQuestion changes

  const renderSimulation = () => {
    switch (scenario.type) {
      case "sms":
        return (
          <SMSSimulation
            scenario={scenario}
            onResponse={handleResponseWithSound}
          />
        );
      case "whatsapp":
        return (
          <WhatsAppSimulation
            scenario={scenario}
            onResponse={handleResponseWithSound}
          />
        );
      case "instagram":
        return (
          <InstagramSimulation
            scenario={scenario}
            onResponse={handleResponseWithSound}
          />
        );
      case "instagram_post":
        return (
          <InstagramPostSimulation
            scenario={scenario}
            onResponse={handleResponseWithSound}
          />
        );
      case "email":
        return (
          <EmailSimulation
            scenario={scenario}
            onResponse={handleResponseWithSound}
          />
        );
      case "call":
        return (
          <CallSimulation
            scenario={scenario}
            onResponse={handleResponseWithSound}
          />
        );
      case "olx":
        return (
          <OLXSimulation
            scenario={scenario}
            onResponse={handleResponseWithSound}
          />
        );
      case "linkedin":
        return (
          <LinkedInSimulation
            scenario={scenario}
            onResponse={handleResponseWithSound}
          />
        );
      case "snapchat":
        return (
          <SnapchatSimulation
            scenario={scenario}
            onResponse={handleResponseWithSound}
          />
        );
      default:
        return <div>Simulation type not supported</div>;
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Progress Header */}
      <div className="bg-black/50 backdrop-blur-xl border-b border-green-500/20 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-500/20 rounded-xl border border-green-500/30">
                <Shield className="w-6 h-6 text-green-400" />
              </div>
              <div>
                <span className="text-green-400 font-semibold text-lg font-mono">
                  {t("digitalSafetyAssessment")}
                </span>
                <div className="text-green-300/70 text-sm font-mono">
                  {t("scenario")} {currentQuestion} of {totalQuestions}
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-green-300 text-sm font-medium font-mono">
                {Math.round((currentQuestion / totalQuestions) * 100)}%
              </div>
              <div className="text-green-400/60 text-xs font-mono">
                Complete
              </div>
            </div>
          </div>
          <div className="w-full bg-gray-900/50 border border-green-500/20 rounded-full h-3 overflow-hidden">
            <div
              className="bg-gradient-to-r from-green-600 via-green-500 to-green-400 h-3 rounded-full transition-all duration-700 ease-out shadow-lg shadow-green-500/50"
              style={{ width: `${(currentQuestion / totalQuestions) * 100}%` }}
            />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8 relative z-10">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-green-400 via-green-300 to-green-200 bg-clip-text text-transparent mb-3 font-mono">
            {t("scenario")} {currentQuestion}
          </h2>
          <p className="text-green-300 text-lg font-mono">
            {t("evaluateSituation")}
          </p>
        </div>

        {renderSimulation()}

        {/* Navigation Hint */}
        <div className="text-center mt-8">
          <div className="inline-flex items-center gap-2 text-green-400/70 text-sm bg-black/30 backdrop-blur-sm px-4 py-2 rounded-full border border-green-500/30 font-mono">
            <span>{t("selectResponse")}</span>
            <ChevronRight className="w-4 h-4" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Survey;
